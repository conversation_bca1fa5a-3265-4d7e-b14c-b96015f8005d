<script setup lang="ts">
import type { Paragraph } from '@/types/paragraph'

defineProps<{ left: Paragraph & { id?: string }, right: Paragraph & { id?: string } }>()
const emit = defineEmits<{ choose: [id: string] }>()

function choose(id: string): void {
  emit('choose', id)
}
</script>

<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <article class="border rounded-lg p-4 bg-white shadow-sm flex flex-col gap-2">
      <header>
        <h3 class="text-lg font-semibold">{{ left.bookTitle }}</h3>
        <p class="text-sm text-gray-500">by {{ left.author }}</p>
      </header>
      <p class="text-gray-800">{{ left.text }}</p>
      <button class="mt-2 px-3 py-1.5 rounded-md bg-emerald-600 text-white hover:bg-emerald-700" @click="choose(left.id!)">Choose</button>
    </article>

    <article class="border rounded-lg p-4 bg-white shadow-sm flex flex-col gap-2">
      <header>
        <h3 class="text-lg font-semibold">{{ right.bookTitle }}</h3>
        <p class="text-sm text-gray-500">by {{ right.author }}</p>
      </header>
      <p class="text-gray-800">{{ right.text }}</p>
      <button class="mt-2 px-3 py-1.5 rounded-md bg-emerald-600 text-white hover:bg-emerald-700" @click="choose(right.id!)">Choose</button>
    </article>
  </div>
</template>
