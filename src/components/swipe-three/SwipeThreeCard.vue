<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBooksStore } from '@/stores/books'
import type { Book } from '@/types/book'

const props = defineProps<{
  book: Book
  transform?: string
}>()

const booksStore = useBooksStore()
const userRating = ref(0)
const hoveredStar = ref(0)

const isBookmarked = computed(() => booksStore.isBookmarked(props.book.id))

function handleBookmarkClick(): void {
  booksStore.toggleBookmark(props.book.id)
}

function handleStarClick(rating: number): void {
  userRating.value = rating
}

function handleStarHover(rating: number): void {
  hoveredStar.value = rating
}

function handleStarLeave(): void {
  hoveredStar.value = 0
}

// Generate array of stars for community rating
const communityStars = computed(() => {
  const rating = props.book.averageRating
  const stars = []
  for (let i = 1; i <= 5; i++) {
    if (i <= rating) {
      stars.push('full')
    } else if (i - 0.5 <= rating) {
      stars.push('half')
    } else {
      stars.push('empty')
    }
  }
  return stars
})

// Generate array for user rating stars
const userStars = computed(() => {
  const activeRating = hoveredStar.value || userRating.value
  return Array.from({ length: 5 }, (_, i) => i + 1 <= activeRating)
})
</script>

<template>
  <div
    class="w-full bg-book-bg rounded-lg border border-book-muted/20 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden select-none cursor-grab active:cursor-grabbing"
    :style="{ transform: transform || '' }"
  >
    <!-- Card Header: Image and Title Section -->
    <div class="flex flex-col sm:flex-row gap-6 p-6">
      <!-- Cover Image -->
      <div class="flex-shrink-0 mx-auto sm:mx-0">
        <div class="aspect-[3/4] w-32 sm:w-40 bg-polar rounded-lg shadow-md border border-swan flex items-center justify-center">
          <span class="text-hare text-sm font-medium">{{ book.coverImageId }}</span>
        </div>
      </div>

      <!-- Text Content -->
      <div class="flex-1 text-center sm:text-left relative">
        <!-- Genre Badge -->
        <div class="absolute top-0 right-0 sm:relative sm:top-auto sm:right-auto sm:mb-2 sm:flex sm:justify-end">
          <span class="inline-block px-3 py-1 bg-book-muted/20 text-book-primary text-sm rounded-full font-medium">
            {{ book.genre }}
          </span>
        </div>

        <!-- Title -->
        <h1 class="text-3xl font-bold text-book-primary mb-2 leading-tight">
          {{ book.title }}
        </h1>

        <!-- Author -->
        <p class="text-book-muted text-lg mb-4">
          by {{ book.author }}
        </p>

        <!-- Bookmark Button -->
        <button
          @click="handleBookmarkClick"
          class="inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-colors font-medium"
          :class="isBookmarked ? 'bg-book-accent text-white hover:bg-book-accent/80' : 'bg-book-muted/10 text-book-primary hover:bg-book-muted/20 border border-book-muted/30'"
        >
          <span class="text-lg">{{ isBookmarked ? '★' : '☆' }}</span>
          <span>{{ isBookmarked ? 'Bookmarked' : 'Bookmark' }}</span>
        </button>
      </div>
    </div>

    <!-- Card Content: The First Paragraph -->
    <div class="px-6 pb-6">
      <blockquote class="border-l-4 border-book-accent pl-6 py-4 bg-book-muted/5 rounded-r-lg">
        <p class="text-book-text/80 text-lg leading-relaxed italic">
          {{ book.firstParagraph }}
        </p>
      </blockquote>
    </div>

    <!-- Card Footer: Ratings -->
    <div class="bg-book-muted/10 px-6 py-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <!-- Community Rating -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-1">
          <template v-for="(star, index) in communityStars" :key="index">
            <div class="relative w-5 h-5">
              <!-- Empty star background -->
              <svg class="w-5 h-5 text-hare absolute" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              <!-- Filled star overlay -->
              <svg
                v-if="star === 'full'"
                class="w-5 h-5 text-book-accent absolute"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              <!-- Half star overlay -->
              <svg
                v-else-if="star === 'half'"
                class="w-5 h-5 text-book-accent absolute overflow-hidden"
                fill="currentColor"
                viewBox="0 0 20 20"
                style="clip-path: inset(0 50% 0 0)"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
          </template>
        </div>
        <div class="flex items-center gap-2 text-book-muted">
          <span class="font-medium">{{ book.averageRating.toFixed(1) }}</span>
          <div class="flex items-center gap-1">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
            </svg>
            <span class="text-sm">{{ book.ratingCount.toLocaleString() }}</span>
          </div>
        </div>
      </div>

      <!-- User Rating -->
      <div class="flex items-center gap-2">
        <span class="text-book-muted text-sm font-medium">Your rating:</span>
        <div class="flex items-center gap-1">
          <template v-for="(filled, index) in userStars" :key="index">
            <button
              @click="handleStarClick(index + 1)"
              @mouseenter="handleStarHover(index + 1)"
              @mouseleave="handleStarLeave"
              class="transition-colors duration-150"
            >
              <svg
                class="w-5 h-5"
                :class="filled ? 'text-book-accent' : 'text-book-muted/40 hover:text-book-accent/50'"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
