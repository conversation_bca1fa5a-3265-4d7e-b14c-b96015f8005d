<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useBooksStore } from '@/stores/books'

const props = defineProps<{
  bookId: string
}>()

const emit = defineEmits<{
  backToParagraph: []
  nextParagraph: []
}>()

const booksStore = useBooksStore()
const isVisible = ref(false)

const book = computed(() => booksStore.getBookById(props.bookId))
const isBookmarked = computed(() => booksStore.isBookmarked(props.bookId))

function handleBookmarkClick(): void {
  booksStore.toggleBookmark(props.bookId)
}

function handleBackToParagraph(): void {
  isVisible.value = false
  setTimeout(() => {
    emit('backToParagraph')
  }, 300)
}

function handleNextParagraph(): void {
  isVisible.value = false
  setTimeout(() => {
    emit('nextParagraph')
  }, 300)
}

function handleBuyClick(): void {
  if (book.value?.amazonUrl) {
    window.open(book.value.amazonUrl, '_blank')
  }
}

onMounted(() => {
  // Animate in
  setTimeout(() => {
    isVisible.value = true
  }, 50)
})
</script>

<template>
  <div
    v-if="book"
    class="absolute inset-0 bg-white transition-transform duration-300 ease-out overflow-y-auto"
    :class="{ 'translate-x-0': isVisible, 'translate-x-full': !isVisible }"
  >
    <!-- Header with navigation buttons -->
    <div class="sticky top-0 bg-white border-b border-swan p-4 z-10">
      <div class="flex items-center justify-between">
        <button
          @click="handleBackToParagraph"
          class="flex items-center gap-2 px-4 py-2 text-macaw hover:bg-polar rounded-xl transition-colors border-2 border-macaw font-bold"
        >
          <span>←</span>
          <span>Back to paragraph</span>
        </button>

        <button
          @click="handleNextParagraph"
          class="flex items-center gap-2 px-4 py-2 bg-feather text-white hover:bg-mask rounded-xl transition-colors font-bold"
        >
          <span>Next paragraph</span>
          <span>→</span>
        </button>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Book cover and basic info -->
      <div class="text-center mb-8">
        <div class="w-40 h-56 bg-polar rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg border-2 border-swan">
          <span class="text-hare text-sm">{{ book.coverImageId }}</span>
        </div>
        <h1 class="text-3xl font-bold text-eel mb-3">{{ book.title }}</h1>
        <p class="text-xl text-wolf mb-3">by {{ book.author }}</p>
        <span class="inline-block px-4 py-2 bg-feather text-white text-lg rounded-full font-bold">
          {{ book.genre }}
        </span>

        <!-- Rating -->
        <div class="flex items-center justify-center mt-4 text-wolf">
          <span class="flex items-center text-lg">
            <span class="text-bee">⭐</span>
            <span class="ml-1">{{ book.averageRating.toFixed(1) }}</span>
            <span class="ml-2">({{ book.ratingCount.toLocaleString() }} ratings)</span>
          </span>
        </div>
      </div>

      <!-- Synopsis -->
      <div v-if="book.synopsis" class="mb-8">
        <h3 class="text-2xl font-bold text-eel mb-4">Synopsis</h3>
        <p class="text-eel leading-relaxed text-lg">{{ book.synopsis }}</p>
      </div>

      <!-- First paragraph -->
      <div class="mb-8">
        <h3 class="text-2xl font-bold text-eel mb-4">Opening Lines</h3>
        <div class="bg-polar rounded-2xl p-6 border-l-4 border-feather">
          <p class="text-eel leading-relaxed text-lg italic">{{ book.firstParagraph }}</p>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="flex flex-col sm:flex-row gap-4 mb-8">
        <button
          @click="handleBookmarkClick"
          class="flex-1 py-4 rounded-2xl transition-colors text-lg font-bold"
          :class="isBookmarked ? 'bg-bee text-eel hover:bg-fox' : 'bg-polar text-eel hover:bg-swan border-2 border-swan'"
        >
          <span class="flex items-center justify-center gap-3">
            <span class="text-xl">{{ isBookmarked ? '★' : '☆' }}</span>
            <span>
              {{ isBookmarked ? 'Bookmarked' : 'Bookmark' }}
            </span>
          </span>
        </button>

        <button
          v-if="book.amazonUrl"
          @click="handleBuyClick"
          class="flex-1 py-4 bg-fox text-white rounded-2xl hover:bg-cardinal transition-colors font-bold text-lg"
        >
          Buy on Amazon
        </button>
      </div>

      <!-- Navigation hint -->
      <div class="text-center text-wolf border-t border-swan pt-6">
        <p class="text-lg mb-2 font-bold text-eel">Ready to continue?</p>
        <p class="text-sm">Use "Back to paragraph" to return to this book's opening, or "Next paragraph" to discover a new book</p>
      </div>
    </div>
  </div>
</template>
