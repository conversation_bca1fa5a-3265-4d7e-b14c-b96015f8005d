<script setup lang="ts">
import type { Book } from '@/types/book'

defineProps<{
  book: Book
  transform?: string
}>()
</script>

<template>
  <div
    class="bg-white rounded-2xl shadow-2xl p-8 select-none cursor-grab active:cursor-grabbing border-2 border-swan transition-all duration-300 ease-out h-full flex flex-col justify-center"
    :style="{ transform: transform || '' }"
  >
    <!-- Quote design for first paragraph -->
    <div class="text-center mb-8">
      <blockquote class="relative">
        <!-- Opening quote mark -->
        <div class="text-6xl text-feather/20 leading-none mb-4">"</div>

        <!-- First paragraph -->
        <p class="text-eel leading-relaxed text-xl md:text-2xl font-medium italic mb-6 px-4">
          {{ book.firstParagraph }}
        </p>

        <!-- Closing quote mark -->
        <div class="text-6xl text-feather/20 leading-none mb-6 text-right">"</div>
      </blockquote>

      <!-- Attribution -->
      <div class="border-t border-swan pt-6">
        <h2 class="text-2xl font-bold text-eel mb-2">{{ book.title }}</h2>
        <p class="text-wolf text-lg">by {{ book.author }}</p>
      </div>
    </div>

    <!-- Swipe indicators -->
    <div class="flex justify-between items-center text-xs mt-auto">
      <div class="flex items-center text-feather bg-polar px-3 py-2 rounded-full">
        <span class="mr-1">👈</span>
        <span class="font-medium">Intrigued</span>
      </div>
      <div class="text-center">
        <p class="text-wolf text-sm">Swipe left if intrigued • Swipe right to skip</p>
        <p class="text-hare text-xs mt-1">Swipe up for details</p>
      </div>
      <div class="flex items-center text-cardinal bg-polar px-3 py-2 rounded-full">
        <span class="font-medium">Skip</span>
        <span class="ml-1">👉</span>
      </div>
    </div>
  </div>
</template>
