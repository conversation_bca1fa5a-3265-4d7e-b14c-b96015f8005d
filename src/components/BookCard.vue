<script setup lang="ts">
import type { Book } from '@/types/book'

defineProps<{ book: Book }>()
</script>

<template>
  <article class="border rounded-lg p-4 bg-white shadow-sm flex flex-col gap-2">
    <header class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">{{ book.title }}</h3>
        <p class="text-sm text-gray-500">by {{ book.author }}</p>
        <p class="text-xs text-blue-600 font-medium">{{ book.genre }}</p>
      </div>
      <div class="text-right">
        <p class="text-sm">⭐ {{ book.averageRating.toFixed(1) }}</p>
        <p class="text-xs text-gray-500">{{ book.ratingCount }} ratings</p>
      </div>
    </header>
    <p class="text-gray-800 line-clamp-4">{{ book.firstParagraph }}</p>
    <footer class="mt-2">
      <RouterLink :to="{ name: 'book', params: { id: book.id } }" class="inline-flex items-center gap-2 px-3 py-1.5 rounded-md bg-blue-600 text-white hover:bg-blue-700">
        Read & rate
      </RouterLink>
    </footer>
  </article>
</template>
