<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import { useBooksStore } from '@/stores/books'

const props = defineProps<{
  bookId: string
  hasTimer?: boolean
}>()

const emit = defineEmits<{
  tap: []
  close: []
}>()

const booksStore = useBooksStore()
const animationKey = ref(0)

const book = computed(() => booksStore.getBookById(props.bookId))
const isBookmarked = computed(() => booksStore.isBookmarked(props.bookId))

// Force animation restart when hasTimer changes to true
watch(() => props.hasTimer, (newValue) => {
  if (newValue) {
    // Force animation restart by changing the key
    animationKey.value++
  }
})

// Also restart animation when bookId changes
watch(() => props.bookId, () => {
  if (props.hasTimer) {
    animationKey.value++
  }
})

function handleBookmarkClick(event: Event): void {
  event.stopPropagation()
  booksStore.toggleBookmark(props.bookId)
}

function handleBannerClick(): void {
  emit('tap')
}
</script>

<template>
  <div
    v-if="book"
    class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg animate-slide-up z-50"
  >
    <div
      class="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
      @click="handleBannerClick"
    >
      <!-- Book info (tappable area) -->
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-gray-900 truncate">{{ book.title }}</h3>
        <p class="text-sm text-gray-600 truncate">by {{ book.author }}</p>
        <p class="text-xs text-blue-600 mt-1">
          {{ hasTimer ? 'Tap for details' : 'Tap for details • No timer' }}
        </p>
      </div>

      <!-- Action buttons -->
      <div class="flex items-center gap-2 ml-4">
        <!-- Bookmark button -->
        <button
          @click="handleBookmarkClick"
          class="px-4 py-2 rounded-md transition-colors flex-shrink-0"
          :class="isBookmarked ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
        >
          <span class="flex items-center gap-2">
            <span>{{ isBookmarked ? '★' : '☆' }}</span>
            <span class="text-sm font-medium">
              {{ isBookmarked ? 'Bookmarked' : 'Bookmark' }}
            </span>
          </span>
        </button>

        <!-- Close button -->
        <button
          @click="emit('close')"
          class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          ✕
        </button>
      </div>
    </div>

    <!-- Progress indicator (only show when hasTimer is true) -->
    <div v-if="hasTimer" class="absolute top-0 left-0 right-0 h-1 bg-gray-200">
      <div :key="animationKey" class="h-full bg-blue-600 animate-progress-bar"></div>
    </div>
  </div>
</template>

<style scoped>
@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes progress-bar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

.animate-progress-bar {
  animation: progress-bar 7s linear;
}
</style>
