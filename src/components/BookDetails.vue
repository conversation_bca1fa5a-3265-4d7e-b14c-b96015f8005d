<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBooksStore } from '@/stores/books'

const props = defineProps<{
  bookId: string
}>()

const emit = defineEmits<{
  close: []
  backToSwipe: []
}>()

const booksStore = useBooksStore()
const isVisible = ref(false)

const book = computed(() => booksStore.getBookById(props.bookId))
const isBookmarked = computed(() => booksStore.isBookmarked(props.bookId))

// Touch handling for swipe down to close
const startY = ref(0)
const currentY = ref(0)
const isDragging = ref(false)
const transform = ref('')

function handleTouchStart(event: TouchEvent): void {
  startY.value = event.touches[0].clientY
  isDragging.value = true
}

function handleTouchMove(event: TouchEvent): void {
  if (!isDragging.value) return
  
  currentY.value = event.touches[0].clientY
  const deltaY = currentY.value - startY.value
  
  // Only allow downward swipes
  if (deltaY > 0) {
    transform.value = `translateY(${deltaY}px)`
  }
}

function handleTouchEnd(): void {
  if (!isDragging.value) return
  
  const deltaY = currentY.value - startY.value
  
  if (deltaY > 100) {
    // Close if swiped down enough
    handleClose()
  } else {
    // Snap back
    transform.value = ''
  }
  
  isDragging.value = false
}

function handleBookmarkClick(): void {
  booksStore.toggleBookmark(props.bookId)
}

function handleBackToSwipe(): void {
  emit('backToSwipe')
}

function handleClose(): void {
  isVisible.value = false
  setTimeout(() => {
    emit('close')
  }, 300)
}

function handleBuyClick(): void {
  if (book.value?.amazonUrl) {
    window.open(book.value.amazonUrl, '_blank')
  }
}

onMounted(() => {
  // Animate in
  setTimeout(() => {
    isVisible.value = true
  }, 50)
})
</script>

<template>
  <div
    v-if="book"
    class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end"
    @click="handleClose"
  >
    <div
      class="bg-white w-full rounded-t-2xl transition-transform duration-300 ease-out"
      :class="{ 'translate-y-0': isVisible, 'translate-y-full': !isVisible }"
      :style="{ transform: transform || '' }"
      @click.stop
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <!-- Handle bar -->
      <div class="flex justify-center pt-3 pb-2">
        <div class="w-12 h-1 bg-gray-300 rounded-full"></div>
      </div>

      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <button
          @click="handleBackToSwipe"
          class="px-3 py-1.5 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
        >
          ← Back to swipe
        </button>
        <button
          @click="handleClose"
          class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          ✕
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 max-h-[70vh] overflow-y-auto">
        <!-- Book cover and basic info -->
        <div class="text-center mb-6">
          <div class="w-32 h-44 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <span class="text-gray-400 text-sm">{{ book.coverImageId }}</span>
          </div>
          <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ book.title }}</h1>
          <p class="text-lg text-gray-600 mb-2">by {{ book.author }}</p>
          <span class="inline-block px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
            {{ book.genre }}
          </span>
          
          <!-- Rating -->
          <div class="flex items-center justify-center mt-3 text-gray-600">
            <span class="flex items-center">
              ⭐ {{ book.averageRating.toFixed(1) }}
              <span class="ml-1">({{ book.ratingCount.toLocaleString() }} ratings)</span>
            </span>
          </div>
        </div>

        <!-- Synopsis -->
        <div v-if="book.synopsis" class="mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Synopsis</h3>
          <p class="text-gray-700 leading-relaxed">{{ book.synopsis }}</p>
        </div>

        <!-- First paragraph -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Opening Lines</h3>
          <p class="text-gray-700 leading-relaxed italic">{{ book.firstParagraph }}</p>
        </div>

        <!-- Action buttons -->
        <div class="flex gap-3">
          <button
            @click="handleBookmarkClick"
            class="flex-1 py-3 rounded-md transition-colors"
            :class="isBookmarked ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            <span class="flex items-center justify-center gap-2">
              <span>{{ isBookmarked ? '★' : '☆' }}</span>
              <span class="font-medium">
                {{ isBookmarked ? 'Bookmarked' : 'Bookmark' }}
              </span>
            </span>
          </button>
          
          <button
            v-if="book.amazonUrl"
            @click="handleBuyClick"
            class="flex-1 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors font-medium"
          >
            Buy on Amazon
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
