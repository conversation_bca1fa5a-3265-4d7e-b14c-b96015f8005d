<script setup lang="ts">
import type { Book } from '@/types/book'

defineProps<{
  book: Book
  transform?: string
}>()
</script>

<template>
  <div
    class="bg-white rounded-2xl shadow-2xl p-6 select-none cursor-grab active:cursor-grabbing"
    :style="{ transform: transform || '' }"
  >
    <!-- Book header -->
    <div class="text-center mb-6">
      <div class="w-20 h-28 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
        <span class="text-gray-400 text-xs">{{ book.coverImageId }}</span>
      </div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">{{ book.title }}</h2>
      <p class="text-gray-600">by {{ book.author }}</p>
      <span class="inline-block px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full mt-2">
        {{ book.genre }}
      </span>
    </div>

    <!-- First paragraph -->
    <div class="mb-6">
      <h3 class="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">Opening Lines</h3>
      <p class="text-gray-800 leading-relaxed text-lg">
        {{ book.firstParagraph }}
      </p>
    </div>

    <!-- Rating -->
    <div class="flex items-center justify-center text-sm text-gray-600">
      <span class="flex items-center">
        ⭐ {{ book.averageRating.toFixed(1) }}
        <span class="ml-1">({{ book.ratingCount.toLocaleString() }} ratings)</span>
      </span>
    </div>

    <!-- Swipe indicators -->
    <div class="flex justify-between items-center mt-6 text-xs text-gray-400">
      <div class="flex items-center">
        <span class="mr-1">👈</span>
        <span>Skip</span>
      </div>
      <div class="flex items-center">
        <span>Intrigued</span>
        <span class="ml-1">👉</span>
      </div>
    </div>
  </div>
</template>
