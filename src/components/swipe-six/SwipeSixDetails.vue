<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBooksStore } from '@/stores/books'
import type { Book } from '@/types/book'

const props = defineProps<{
  book: Book
  verticalProgress: number  // 0 to 1
}>()

const emit = defineEmits<{
  close: []
}>()

const booksStore = useBooksStore()
const userRating = ref(0)

const isBookmarked = computed(() => booksStore.isBookmarked(props.book.id))

// Generate theme color based on book genre
const themeColor = computed(() => {
  const colors = {
    'Fiction': '#1CB0F6',           // Macaw blue
    'Science Fiction': '#CE82FF',   // Beetle purple
    'Fantasy': '#FF9600',           // Fox orange
    'Romance': '#FF4B4B',           // Cardinal red
    'Mystery': '#4B4B4B',           // Eel dark
    'Thriller': '#2B70C9',          // Humpback blue
    'Horror': '#777777',            // <PERSON> gray
    'Adventure': '#58CC02',         // Feather green
    'Historical Fiction': '#FFC800', // <PERSON> yellow
    'Literary Fiction': '#CE82FF',   // Beetle purple
    'Young Adult': '#1CB0F6',       // Macaw blue
    'Non-Fiction': '#777777',       // Wolf gray
    'Biography': '#FFC800',         // Bee yellow
    'Self-Help': '#58CC02',         // Feather green
    'Philosophy': '#2B70C9'         // Humpback blue
  }
  return colors[props.book.genre as keyof typeof colors] || '#1CB0F6'
})

// Shared element (thumbnail) position and scale
const thumbnailTransform = computed(() => {
  // Start from center-large, end at top-left-small
  const startScale = 1
  const endScale = 0.3
  const scale = startScale + (endScale - startScale) * props.verticalProgress

  const startX = 0
  const endX = -35 // Move to left
  const translateX = startX + (endX - startX) * props.verticalProgress

  const startY = 0
  const endY = -35 // Move up
  const translateY = startY + (endY - startY) * props.verticalProgress

  return `scale(${scale}) translateX(${translateX}%) translateY(${translateY}%)`
})

// Details panel transform
const panelTransform = computed(() => {
  const translateY = 100 - (props.verticalProgress * 100)
  return `translateY(${translateY}%)`
})

// Staggered content animations
const getContentDelay = (index: number) => {
  return Math.max(0, (props.verticalProgress - 0.3) * (1 / 0.7)) * (1 - index * 0.1)
}

function handleBookmarkClick(): void {
  booksStore.toggleBookmark(props.book.id)
}

function handleRatingClick(rating: number): void {
  userRating.value = rating
}

function handleClose(): void {
  emit('close')
}

// Generate array of stars for community rating
const communityStars = computed(() => {
  const rating = props.book.averageRating
  const stars = []
  for (let i = 1; i <= 5; i++) {
    if (i <= rating) {
      stars.push('full')
    } else if (i - 0.5 <= rating) {
      stars.push('half')
    } else {
      stars.push('empty')
    }
  }
  return stars
})

// Generate array for user rating stars
const userStars = computed(() => {
  return Array.from({ length: 5 }, (_, i) => i + 1 <= userRating.value)
})
</script>

<template>
  <div class="absolute inset-0 overflow-hidden">
    <!-- Background with theme color -->
    <div
      class="absolute inset-0"
      :style="{ backgroundColor: themeColor }"
    ></div>

    <!-- Quote content that slides up (similar to main slide) -->
    <div
      class="absolute inset-0 flex items-center justify-center px-8 transition-all duration-500"
      :style="{
        transform: `translateY(${-verticalProgress * 60}%)`,
        opacity: Math.max(0.3, 1 - verticalProgress * 0.7)
      }"
    >
      <div class="text-center text-white max-w-4xl">
        <blockquote class="relative">
          <!-- Opening quote mark -->
          <div class="text-6xl text-white/20 leading-none mb-4">"</div>

          <!-- First paragraph (smaller in details) -->
          <p class="text-white leading-relaxed text-lg md:text-xl font-medium italic mb-6 px-4">
            {{ book.firstParagraph }}
          </p>

          <!-- Closing quote mark -->
          <div class="text-6xl text-white/20 leading-none mb-6 text-right">"</div>
        </blockquote>

        <!-- Attribution -->
        <div class="border-t border-white/30 pt-4">
          <h2 class="text-2xl md:text-3xl font-bold text-white mb-2">{{ book.title }}</h2>
          <p class="text-white/90 text-lg">by {{ book.author }}</p>
        </div>
      </div>
    </div>

    <!-- Details Panel (slides up from bottom) -->
    <div
      class="absolute inset-x-0 bottom-0 h-2/3 bg-white/95 backdrop-blur-md rounded-t-3xl"
      :style="{ transform: panelTransform }"
    >
      <div class="h-full overflow-y-auto">
        <!-- Close handle -->
        <div class="flex justify-center pt-6 pb-4">
          <button
            @click="handleClose"
            class="w-16 h-1.5 bg-gray-300 rounded-full hover:bg-gray-400 transition-colors"
          ></button>
        </div>

        <div class="px-8 pb-8">
          <!-- Header with book info -->
          <div class="text-center mb-8">
            <div
              class="transition-all duration-500"
              :style="{
                opacity: getContentDelay(0),
                transform: `translateY(${(1 - getContentDelay(0)) * 20}px)`
              }"
            >
              <h2 class="text-3xl font-bold text-eel mb-2">{{ book.title }}</h2>
              <p class="text-wolf mb-3 text-xl">by {{ book.author }}</p>
              <span class="inline-block px-4 py-2 rounded-full text-sm font-bold text-white"
                    :style="{ backgroundColor: themeColor }">
                {{ book.genre }}
              </span>
            </div>
          </div>

          <!-- Rating Section -->
          <div
            class="mb-6 transition-all duration-500"
            :style="{
              opacity: getContentDelay(1),
              transform: `translateY(${(1 - getContentDelay(1)) * 20}px)`
            }"
          >
            <div class="flex items-center gap-2 mb-2">
              <div class="flex items-center gap-1">
                <template v-for="(star, index) in communityStars" :key="index">
                  <div class="relative w-5 h-5">
                    <svg class="w-5 h-5 text-hare absolute" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <svg
                      v-if="star === 'full'"
                      class="w-5 h-5 text-bee absolute"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <svg
                      v-else-if="star === 'half'"
                      class="w-5 h-5 text-bee absolute overflow-hidden"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      style="clip-path: inset(0 50% 0 0)"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                  </div>
                </template>
              </div>
              <span class="text-eel font-bold text-lg">{{ book.averageRating.toFixed(1) }}</span>
              <span class="text-wolf text-sm">({{ book.ratingCount.toLocaleString() }} ratings)</span>
            </div>
          </div>

          <!-- Action Buttons -->
          <div
            class="flex gap-4 mb-8 transition-all duration-500"
            :style="{
              opacity: getContentDelay(2),
              transform: `translateY(${(1 - getContentDelay(2)) * 20}px)`
            }"
          >
            <button
              class="flex-1 py-4 px-8 rounded-2xl font-bold text-white text-lg transition-all duration-300 hover:scale-105 shadow-lg"
              :style="{ backgroundColor: themeColor }"
            >
              Start Reading
            </button>
            <button
              @click="handleBookmarkClick"
              class="px-6 py-4 rounded-2xl font-bold transition-all duration-300 hover:scale-105 shadow-lg"
              :class="isBookmarked ? 'bg-bee text-eel hover:bg-fox' : 'bg-white text-eel hover:bg-gray-50 border-2 border-gray-200'"
            >
              <span class="text-2xl">{{ isBookmarked ? '★' : '☆' }}</span>
            </button>
          </div>

          <!-- Synopsis -->
          <div
            class="mb-6 transition-all duration-500"
            :style="{
              opacity: getContentDelay(3),
              transform: `translateY(${(1 - getContentDelay(3)) * 20}px)`
            }"
          >
            <h3 class="text-xl font-bold text-eel mb-3">Synopsis</h3>
            <p class="text-eel leading-relaxed text-base">
              {{ book.synopsis || book.firstParagraph }}
            </p>
          </div>

          <!-- Your Rating -->
          <div
            class="transition-all duration-500"
            :style="{
              opacity: getContentDelay(4),
              transform: `translateY(${(1 - getContentDelay(4)) * 20}px)`
            }"
          >
            <h3 class="text-xl font-bold text-eel mb-3">Your Rating</h3>
            <div class="flex items-center gap-1">
              <template v-for="(filled, index) in userStars" :key="index">
                <button
                  @click="handleRatingClick(index + 1)"
                  class="transition-colors duration-150"
                >
                  <svg
                    class="w-8 h-8"
                    :class="filled ? 'text-bee' : 'text-hare hover:text-bee/70'"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
