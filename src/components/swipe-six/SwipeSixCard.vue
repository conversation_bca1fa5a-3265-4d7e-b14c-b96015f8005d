<script setup lang="ts">
import { computed } from 'vue'
import type { Book } from '@/types/book'

const props = defineProps<{
  book: Book
  nextBook?: Book
  prevBook?: Book
  horizontalProgress: number  // -1 to 1
  verticalProgress: number    // 0 to 1
  isDragging: boolean
}>()

// Generate theme colors based on book genre
const getThemeColor = (book: Book) => {
  const colors = {
    'Fiction': '#1CB0F6',           // Macaw blue
    'Science Fiction': '#CE82FF',   // Beetle purple
    'Fantasy': '#FF9600',           // <PERSON> orange
    'Romance': '#FF4B4B',           // Cardinal red
    'Mystery': '#4B4B4B',           // Eel dark
    'Thriller': '#2B70C9',          // Humpback blue
    'Horror': '#777777',            // Wolf gray
    'Adventure': '#58CC02',         // Feather green
    'Historical Fiction': '#FFC800', // Bee yellow
    'Literary Fiction': '#CE82FF',   // Beetle purple
    'Young Adult': '#1CB0F6',       // Macaw blue
    'Non-Fiction': '#777777',       // <PERSON> gray
    'Biography': '#FFC800',         // <PERSON> yellow
    'Self-Help': '#58CC02',         // Feather green
    'Philosophy': '#2B70C9'         // Humpback blue
  }
  return colors[book.genre as keyof typeof colors] || '#1CB0F6'
}

// Current book theme
const currentTheme = computed(() => getThemeColor(props.book))

// Next/prev book themes for transitions
const nextTheme = computed(() => props.nextBook ? getThemeColor(props.nextBook) : currentTheme.value)
const prevTheme = computed(() => props.prevBook ? getThemeColor(props.prevBook) : currentTheme.value)

// Background color with cross-fade
const backgroundColor = computed(() => {
  if (props.horizontalProgress === 0) return currentTheme.value

  if (props.horizontalProgress > 0) {
    // Swiping right, show next book color
    const opacity = Math.abs(props.horizontalProgress)
    return `color-mix(in srgb, ${nextTheme.value} ${opacity * 100}%, ${currentTheme.value} ${(1 - opacity) * 100}%)`
  } else {
    // Swiping left, show prev book color
    const opacity = Math.abs(props.horizontalProgress)
    return `color-mix(in srgb, ${prevTheme.value} ${opacity * 100}%, ${currentTheme.value} ${(1 - opacity) * 100}%)`
  }
})

// Image transforms (1x speed)
const currentImageTransform = computed(() => {
  const translateX = props.horizontalProgress * 100 // Move with swipe
  const scale = 1 - (props.verticalProgress * 0.7) // Scale down for details transition
  const translateY = props.verticalProgress * -40 // Move up for details

  return `translateX(${translateX}%) scale(${scale}) translateY(${translateY}%)`
})

const nextImageTransform = computed(() => {
  if (props.horizontalProgress <= 0) return 'translateX(100%)'
  const translateX = 100 - (props.horizontalProgress * 100)
  return `translateX(${translateX}%)`
})

const prevImageTransform = computed(() => {
  if (props.horizontalProgress >= 0) return 'translateX(-100%)'
  const translateX = -100 - (props.horizontalProgress * 100)
  return `translateX(${translateX}%)`
})

// Text transforms (1.2x speed for parallax)
const currentTextTransform = computed(() => {
  const translateX = props.horizontalProgress * 120 // 1.2x speed for parallax
  const translateY = props.verticalProgress * -100 // Move up and out for details
  return `translateX(${translateX}%) translateY(${translateY}%)`
})

const nextTextTransform = computed(() => {
  if (props.horizontalProgress <= 0) return 'translateX(120%)'
  const translateX = 120 - (props.horizontalProgress * 120)
  return `translateX(${translateX}%)`
})

const prevTextTransform = computed(() => {
  if (props.horizontalProgress >= 0) return 'translateX(-120%)'
  const translateX = -120 - (props.horizontalProgress * 120)
  return `translateX(${translateX}%)`
})

// Opacity calculations
const currentOpacity = computed(() => {
  const horizontalOpacity = 1 - Math.abs(props.horizontalProgress)
  const verticalOpacity = 1 - props.verticalProgress
  return Math.min(horizontalOpacity, verticalOpacity)
})

const nextOpacity = computed(() => {
  return props.horizontalProgress > 0 ? props.horizontalProgress : 0
})

const prevOpacity = computed(() => {
  return props.horizontalProgress < 0 ? Math.abs(props.horizontalProgress) : 0
})

// Content type based on book properties
const getContentType = (book: Book) => {
  if (book.averageRating >= 4.5) return 'BESTSELLER'
  if (book.ratingCount > 50000) return 'POPULAR'
  if (book.genre === 'Fiction') return 'NOVEL'
  return 'BOOK'
}
</script>

<template>
  <div class="relative h-full w-full overflow-hidden">
    <!-- Layer 1: Background Color (Cross-fade) -->
    <div
      class="absolute inset-0 transition-colors"
      :class="{ 'duration-300 ease-out': !isDragging }"
      :style="{ backgroundColor }"
    ></div>

    <!-- Layer 2: Main Content (1x speed) - Quote Design -->
    <!-- Current Book Content -->
    <div
      class="absolute inset-0 flex items-center justify-center px-8"
      :class="{ 'transition-all duration-300 ease-out': !isDragging }"
      :style="{
        transform: currentImageTransform,
        opacity: currentOpacity
      }"
    >
      <div class="text-center text-white max-w-4xl">
        <blockquote class="relative">
          <!-- Opening quote mark -->
          <div class="text-8xl text-white/30 leading-none mb-6">"</div>

          <!-- First paragraph -->
          <p class="text-white leading-relaxed text-2xl md:text-3xl font-medium italic mb-8 px-4">
            {{ book.firstParagraph }}
          </p>

          <!-- Closing quote mark -->
          <div class="text-8xl text-white/30 leading-none mb-8 text-right">"</div>
        </blockquote>

        <!-- Attribution -->
        <div class="border-t border-white/30 pt-6">
          <h2 class="text-3xl md:text-4xl font-bold text-white mb-2">{{ book.title }}</h2>
          <p class="text-white/90 text-xl">by {{ book.author }}</p>
        </div>
      </div>
    </div>

    <!-- Next Book Content -->
    <div
      v-if="nextBook && horizontalProgress > 0"
      class="absolute inset-0 flex items-center justify-center px-8"
      :class="{ 'transition-all duration-300 ease-out': !isDragging }"
      :style="{
        transform: nextImageTransform,
        opacity: nextOpacity
      }"
    >
      <div class="text-center text-white max-w-4xl">
        <blockquote class="relative">
          <div class="text-8xl text-white/30 leading-none mb-6">"</div>
          <p class="text-white leading-relaxed text-2xl md:text-3xl font-medium italic mb-8 px-4">
            {{ nextBook.firstParagraph }}
          </p>
          <div class="text-8xl text-white/30 leading-none mb-8 text-right">"</div>
        </blockquote>
        <div class="border-t border-white/30 pt-6">
          <h2 class="text-3xl md:text-4xl font-bold text-white mb-2">{{ nextBook.title }}</h2>
          <p class="text-white/90 text-xl">by {{ nextBook.author }}</p>
        </div>
      </div>
    </div>

    <!-- Previous Book Content -->
    <div
      v-if="prevBook && horizontalProgress < 0"
      class="absolute inset-0 flex items-center justify-center px-8"
      :class="{ 'transition-all duration-300 ease-out': !isDragging }"
      :style="{
        transform: prevImageTransform,
        opacity: prevOpacity
      }"
    >
      <div class="text-center text-white max-w-4xl">
        <blockquote class="relative">
          <div class="text-8xl text-white/30 leading-none mb-6">"</div>
          <p class="text-white leading-relaxed text-2xl md:text-3xl font-medium italic mb-8 px-4">
            {{ prevBook.firstParagraph }}
          </p>
          <div class="text-8xl text-white/30 leading-none mb-8 text-right">"</div>
        </blockquote>
        <div class="border-t border-white/30 pt-6">
          <h2 class="text-3xl md:text-4xl font-bold text-white mb-2">{{ prevBook.title }}</h2>
          <p class="text-white/90 text-xl">by {{ prevBook.author }}</p>
        </div>
      </div>
    </div>





    <!-- Pull-up indicator -->
    <div
      class="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white/70 animate-bounce"
      :style="{ opacity: 1 - verticalProgress }"
    >
      <div class="text-center">
        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
        </svg>
        <p class="text-sm font-medium">Swipe up for details</p>
      </div>
    </div>
  </div>
</template>
