<script setup lang="ts">
import { computed } from 'vue'

const ratingModel = defineModel<number>({
  default: 0,
  set: (v) => Math.min(5, Math.max(1, v)),
})

const stars = computed<number[]>(() => [1, 2, 3, 4, 5])

function setRating(value: number): void {
  ratingModel.value = value
}
</script>

<template>
  <div class="flex items-center gap-1">
    <button
      v-for="s in stars"
      :key="s"
      type="button"
      class="size-8 rounded-full flex items-center justify-center text-yellow-500 hover:scale-105 transition"
      :class="{ 'bg-yellow-100': ratingModel >= s }"
      @click="setRating(s)"
      aria-label="Rate"
    >
      <span class="i-heroicons-star-20-solid" v-if="ratingModel >= s">★</span>
      <span class="opacity-40" v-else>☆</span>
    </button>
  </div>
</template>
