<script setup lang="ts">
import type { Paragraph } from '@/types/paragraph'

defineProps<{ paragraph: Paragraph & { id?: string } }>()
</script>

<template>
  <article class="border rounded-lg p-4 bg-white shadow-sm flex flex-col gap-2">
    <header class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">{{ paragraph.bookTitle }}</h3>
        <p class="text-sm text-gray-500">by {{ paragraph.author }}</p>
      </div>
      <div class="text-right">
        <p class="text-sm">⭐ {{ (paragraph.avgRating ?? 0).toFixed(1) }}</p>
        <p class="text-xs text-gray-500">{{ paragraph.ratingsCount ?? 0 }} ratings</p>
      </div>
    </header>
    <p class="text-gray-800 line-clamp-4">{{ paragraph.text }}</p>
    <footer class="mt-2">
      <RouterLink :to="{ name: 'paragraph', params: { id: paragraph.id } }" class="inline-flex items-center gap-2 px-3 py-1.5 rounded-md bg-blue-600 text-white hover:bg-blue-700">
        Read & rate
      </RouterLink>
    </footer>
  </article>
</template>
