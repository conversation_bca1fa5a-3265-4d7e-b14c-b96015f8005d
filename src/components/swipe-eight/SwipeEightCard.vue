<script setup lang="ts">
import { computed } from 'vue'
import type { Book } from '@/types/book'

interface CardData {
  id: string
  title: string
  content: string
  color: string
  book: Book
  displayIndex: number
}

const props = defineProps<{
  card: CardData
  index: number
  currentIndex: number
  cardsLength: number
}>()

const cardNumber = computed(() => {
  return ((((props.currentIndex + props.index) % props.cardsLength) + props.cardsLength) % props.cardsLength) + 1
})

const boxShadow = computed(() => {
  if (props.index === 0) {
    return '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 15px 30px -15px rgba(0, 0, 0, 0.4)'
  }
  return `0 ${8 + props.index * 4}px ${16 + props.index * 8}px -4px rgba(0, 0, 0, ${0.15 + props.index * 0.05})`
})

const pointerEvents = computed(() => {
  return props.index === 0 ? 'auto' : 'none'
})
</script>

<template>
  <div
    class="w-full h-full rounded-2xl p-6 flex flex-col justify-between text-white border border-white/20"
    :class="card.color"
    :style="{
      pointerEvents,
      boxShadow
    }"
  >
    <div>
      <h3 class="text-xl sm:text-2xl font-bold mb-3">{{ card.title }}</h3>
      <p class="text-base sm:text-lg opacity-90 leading-relaxed line-clamp-6">
        {{ card.content }}
      </p>
    </div>
    
    <!-- Book metadata -->
    <div class="mt-4 space-y-2">
      <div class="flex items-center justify-between text-sm opacity-75">
        <span>by {{ card.book.author }}</span>
        <span>{{ card.book.genre }}</span>
      </div>
      
      <!-- Rating -->
      <div class="flex items-center gap-2 text-sm opacity-75">
        <div class="flex items-center gap-1">
          <template v-for="star in 5" :key="star">
            <svg 
              class="w-3 h-3" 
              :class="star <= Math.round(card.book.averageRating) ? 'text-bee' : 'text-white/30'"
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </template>
          <span class="ml-1">{{ card.book.averageRating.toFixed(1) }}</span>
        </div>
        <span>({{ card.book.ratingCount.toLocaleString() }} reviews)</span>
      </div>
      
      <div class="text-sm opacity-75">
        Card {{ cardNumber }} of ∞
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-6 {
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
