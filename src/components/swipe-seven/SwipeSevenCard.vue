<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useBooksStore } from '@/stores/books'
import type { Book } from '@/types/book'

const props = defineProps<{
  book: Book
  transform?: string
  isExpanded: boolean
  verticalProgress?: number
}>()

const emit = defineEmits<{
  expand: []
  collapse: []
}>()

const booksStore = useBooksStore()
const userRating = ref(0)

// Template refs for FLIP animation
const largePoster = ref<HTMLElement>()
const smallPoster = ref<HTMLElement>()

const isBookmarked = computed(() => booksStore.isBookmarked(props.book.id))

// Generate theme color based on book genre (solid colors)
const themeColor = computed(() => {
  const colors = {
    'Fiction': '#1CB0F6',           // Macaw blue
    'Science Fiction': '#CE82FF',   // Beetle purple
    'Fantasy': '#FF9600',           // <PERSON> orange
    'Romance': '#FF4B4B',           // <PERSON> red
    'Mystery': '#4B4B4B',           // Eel dark
    'Thriller': '#2B70C9',          // Humpback blue
    'Horror': '#777777',            // <PERSON> gray
    'Adventure': '#58CC02',         // Feather green
    'Historical Fiction': '#FFC800', // Bee yellow
    'Literary Fiction': '#CE82FF',   // Beetle purple
    'Young Adult': '#1CB0F6',       // Macaw blue
    'Non-Fiction': '#777777',       // Wolf gray
    'Biography': '#FFC800',         // Bee yellow
    'Self-Help': '#58CC02',         // Feather green
    'Philosophy': '#2B70C9'         // Humpback blue
  }
  return colors[props.book.genre as keyof typeof colors] || '#1CB0F6'
})

// Generate content type based on book properties
const contentType = computed(() => {
  if (props.book.averageRating >= 4.5) return 'BESTSELLER'
  if (props.book.ratingCount > 50000) return 'POPULAR'
  if (props.book.genre === 'Fiction') return 'NOVEL'
  return 'BOOK'
})

function handleBookmarkClick(): void {
  booksStore.toggleBookmark(props.book.id)
}

function handleRatingClick(rating: number): void {
  userRating.value = rating
}

function handleExpandClick(): void {
  if (props.isExpanded) {
    emit('collapse')
  } else {
    // FLIP Animation Logic
    performFLIPAnimation()
  }
}

async function performFLIPAnimation(): Promise<void> {
  if (!largePoster.value || !smallPoster.value) return

  // 1. FIRST: Get the position of the large poster BEFORE the state change
  const first = largePoster.value.getBoundingClientRect()

  // Change the state, which triggers Vue to re-render the DOM
  emit('expand')

  // 2. LAST: Wait for Vue's DOM update, then get the final position of the small thumbnail
  await nextTick()

  const last = smallPoster.value.getBoundingClientRect()

  // 3. INVERT: Calculate the difference in position and scale
  const deltaX = first.left - last.left
  const deltaY = first.top - last.top
  const deltaW = first.width / last.width
  const deltaH = first.height / last.height

  // Apply the inverted transform to the SMALL thumbnail
  smallPoster.value.style.transformOrigin = 'top left'
  smallPoster.value.style.transform = `translate(${deltaX}px, ${deltaY}px) scale(${deltaW}, ${deltaH})`

  // 4. PLAY: In the next frame, remove the inverted transform and add transition
  requestAnimationFrame(() => {
    if (smallPoster.value) {
      smallPoster.value.style.transition = 'transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)'
      smallPoster.value.style.transform = 'none'
    }
  })

  // Clean up after animation
  const cleanup = () => {
    if (smallPoster.value) {
      smallPoster.value.style.transition = ''
      smallPoster.value.style.transformOrigin = ''
    }
  }

  smallPoster.value.addEventListener('transitionend', cleanup, { once: true })
}

// Generate array of stars for community rating
const communityStars = computed(() => {
  const rating = props.book.averageRating
  const stars = []
  for (let i = 1; i <= 5; i++) {
    if (i <= rating) {
      stars.push('full')
    } else if (i - 0.5 <= rating) {
      stars.push('half')
    } else {
      stars.push('empty')
    }
  }
  return stars
})

// Generate array for user rating stars
const userStars = computed(() => {
  return Array.from({ length: 5 }, (_, i) => i + 1 <= userRating.value)
})
</script>

<template>
  <div
    class="movie-card-wrapper"
    :class="{ 'details-visible': isExpanded }"
    :style="{ transform: transform || '', backgroundColor: themeColor }"
    @click="handleExpandClick"
  >
    <!-- === SUMMARY VIEW (Visible by default) === -->
    <div class="summary-view">
      <!-- The large poster image -->
      <div
        ref="largePoster"
        class="large-poster"
      >
        <div class="w-64 h-80 bg-white/20 backdrop-blur-sm rounded-3xl shadow-2xl flex items-center justify-center border-2 border-white/30">
          <div class="text-center">
            <div class="w-48 h-60 bg-white/30 rounded-2xl mb-4 flex items-center justify-center">
              <span class="text-white text-lg font-bold">{{ book.coverImageId }}</span>
            </div>
            <div class="w-16 h-16 rounded-full bg-white/30 backdrop-blur-sm flex items-center justify-center border-2 border-white/50 mx-auto">
              <svg class="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 5v10l7-5-7-5z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- The original title that will animate out -->
      <div class="summary-text">
        <h2 class="title">{{ book.title }}</h2>
        <p class="subtitle">{{ contentType }}</p>
      </div>
    </div>

    <!-- === DETAILS VIEW (Initially hidden below screen) === -->
    <div class="details-panel">
      <!-- The small thumbnail image that the large one transforms into -->
      <div
        ref="smallPoster"
        class="small-poster-thumbnail"
      >
        <div class="w-20 h-24 bg-white/20 backdrop-blur-sm rounded-lg shadow-lg flex items-center justify-center border border-white/30">
          <span class="text-white text-xs font-bold">{{ book.coverImageId }}</span>
        </div>
      </div>

      <div class="details-content">
        <!-- These elements will stagger-fade in -->
        <div class="detail-item" style="--delay: 0.1s;">
          <div class="flex items-center justify-center gap-1 mb-2">
            <template v-for="(star, index) in communityStars" :key="index">
              <div class="relative w-5 h-5">
                <svg class="w-5 h-5 text-white/30 absolute" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg
                  v-if="star === 'full'"
                  class="w-5 h-5 text-bee absolute"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
            </template>
            <span class="text-white font-bold text-lg ml-2">{{ book.averageRating.toFixed(1) }}</span>
          </div>
        </div>

        <div class="detail-item" style="--delay: 0.2s;">
          <button class="w-full bg-white/20 backdrop-blur-sm text-white py-3 px-6 rounded-2xl font-bold hover:bg-white/30 transition-all duration-300 hover:scale-105 shadow-lg border border-white/30 mb-3">
            Start Reading
          </button>
        </div>

        <div class="detail-item" style="--delay: 0.3s;">
          <button
            @click.stop="handleBookmarkClick"
            class="w-full px-6 py-3 rounded-2xl font-bold transition-all duration-300 hover:scale-105 shadow-lg mb-4"
            :class="isBookmarked ? 'bg-bee text-eel hover:bg-fox' : 'bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 border border-white/30'"
          >
            <span class="text-xl mr-2">{{ isBookmarked ? '★' : '☆' }}</span>
            {{ isBookmarked ? 'Bookmarked' : 'Add to List' }}
          </button>
        </div>

        <div class="detail-item" style="--delay: 0.4s;">
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
            <p class="text-white leading-relaxed text-sm">
              {{ book.synopsis || book.firstParagraph }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Pull-up indicator (only visible when collapsed) -->
    <div
      v-if="!isExpanded"
      class="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white/70 animate-bounce"
    >
      <div class="text-center">
        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
        </svg>
        <p class="text-sm font-medium">Swipe up for details</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* --- Base Container --- */
.movie-card-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden; /* Crucial to hide the panel when it's off-screen */
  cursor: pointer;
  transition: transform 0.3s ease-out;
}

/* --- Summary View Elements (Initial State) --- */
.summary-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
}

.large-poster {
  margin-bottom: 2rem;
  /* We will control this with JS for the FLIP animation */
  transition: opacity 0.3s ease-in-out;
}

.summary-text {
  text-align: center;
  color: white;
  transition: transform 0.5s ease-in-out, opacity 0.4s ease-in-out;
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.2rem;
  font-weight: 600;
  opacity: 0.9;
  letter-spacing: 0.1em;
}

/* --- Details Panel (Initial State) --- */
.details-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80%; /* Or however tall you want it */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px;
  padding-top: 80px; /* Leave space for the thumbnail */

  /* Initially hidden below the screen */
  transform: translateY(100%);
  transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.small-poster-thumbnail {
  position: absolute;
  top: 20px;
  left: 20px;
  /* We control this with JS for FLIP, but a fallback is good */
  opacity: 0;
  transition: opacity 0.3s ease-in-out 0.2s;
}

.details-content {
  height: 100%;
  overflow-y: auto;
}

.detail-item {
  /* Initially invisible and slightly down */
  opacity: 0;
  transform: translateY(20px);
  /* Use the custom property for staggered delay */
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
  transition-delay: var(--delay);
}

/*
===========================================================
=== THE ACTIVE STATE: When '.details-visible' is added ===
===========================================================
*/

.details-visible .summary-text {
  /* 1. The original text slides UP and FADES OUT */
  transform: translateY(-100px);
  opacity: 0;
}

.details-visible .details-panel {
  /* 2. The panel slides UP into view */
  transform: translateY(0);
}

/* Hide the large poster to avoid flicker while the small one animates */
.details-visible .large-poster {
  opacity: 0;
}

.details-visible .small-poster-thumbnail {
  opacity: 1;
}

.details-visible .detail-item {
  /* 4. The new content fades IN and slides UP into place */
  opacity: 1;
  transform: translateY(0);
}
</style>
