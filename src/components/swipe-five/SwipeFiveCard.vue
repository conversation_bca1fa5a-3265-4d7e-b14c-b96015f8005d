<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBooksStore } from '@/stores/books'
import type { Book } from '@/types/book'

const props = defineProps<{
  book: Book
  transform?: string
  isExpanded: boolean
  verticalProgress?: number
}>()

const emit = defineEmits<{
  expand: []
  collapse: []
}>()

const booksStore = useBooksStore()
const userRating = ref(0)

const isBookmarked = computed(() => booksStore.isBookmarked(props.book.id))

// Generate theme color based on book genre (solid colors like swipe-six)
const themeColor = computed(() => {
  const colors = {
    'Fiction': '#1CB0F6',           // Macaw blue
    'Science Fiction': '#CE82FF',   // Beetle purple
    'Fantasy': '#FF9600',           // Fox orange
    'Romance': '#FF4B4B',           // Cardinal red
    'Mystery': '#4B4B4B',           // Eel dark
    'Thriller': '#2B70C9',          // Humpback blue
    'Horror': '#777777',            // <PERSON> gray
    'Adventure': '#58CC02',         // Feather green
    'Historical Fiction': '#FFC800', // Bee yellow
    'Literary Fiction': '#CE82FF',   // Beetle purple
    'Young Adult': '#1CB0F6',       // Macaw blue
    'Non-Fiction': '#777777',       // Wolf gray
    'Biography': '#FFC800',         // Bee yellow
    'Self-Help': '#58CC02',         // Feather green
    'Philosophy': '#2B70C9'         // Humpback blue
  }
  return colors[props.book.genre as keyof typeof colors] || '#1CB0F6'
})

// Generate content type based on book properties
const contentType = computed(() => {
  if (props.book.averageRating >= 4.5) return 'BESTSELLER'
  if (props.book.ratingCount > 50000) return 'POPULAR'
  if (props.book.genre === 'Fiction') return 'NOVEL'
  return 'BOOK'
})

function handleBookmarkClick(): void {
  booksStore.toggleBookmark(props.book.id)
}

function handleRatingClick(rating: number): void {
  userRating.value = rating
}

// Generate array of stars for community rating
const communityStars = computed(() => {
  const rating = props.book.averageRating
  const stars = []
  for (let i = 1; i <= 5; i++) {
    if (i <= rating) {
      stars.push('full')
    } else if (i - 0.5 <= rating) {
      stars.push('half')
    } else {
      stars.push('empty')
    }
  }
  return stars
})

// Generate array for user rating stars
const userStars = computed(() => {
  return Array.from({ length: 5 }, (_, i) => i + 1 <= userRating.value)
})
</script>

<template>
  <div
    class="relative h-full w-full overflow-hidden select-none"
    :style="{ transform: transform || '' }"
  >
    <!-- Background with solid theme color -->
    <div
      class="absolute inset-0"
      :style="{ backgroundColor: themeColor }"
    ></div>

    <!-- Content Container -->
    <div class="relative h-full flex flex-col">
      <!-- Collapsed State Content -->
      <div
        class="absolute inset-0 flex flex-col justify-center items-center text-white px-8"
        :class="{ 'pointer-events-none': isExpanded || (props.verticalProgress && props.verticalProgress > 0.1) }"
        :style="{
          transform: isExpanded ? 'translateY(-300px)' : `translateY(${-(props.verticalProgress || 0) * 300}px)`,
          opacity: isExpanded ? 0.5 : Math.max(0.5, 1 - (props.verticalProgress || 0) * 0.5),
          transition: props.verticalProgress && !isExpanded ? 'none' : 'all 0.5s ease-out',
          zIndex: 1
        }"
      >


        <!-- Card Content: The First Paragraph -->
        <div class="px-6 pb-6">
          <blockquote class="border-l-4 border-book-accent pl-6 py-4 bg-book-muted/5 rounded-r-lg">
            <p class="text-book-text/80 text-lg leading-relaxed italic">
              {{ book.firstParagraph }}
            </p>
          </blockquote>
        </div>

        <!-- Expand Indicator -->
        <div
          class="mt-8 animate-bounce"
          :style="{
            opacity: 1 - (props.verticalProgress || 0),
            transition: props.verticalProgress ? 'none' : 'opacity 0.3s ease-out'
          }"
        >
          <svg class="w-6 h-6 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
          </svg>
        </div>
      </div>

      <!-- Expanded State Detail Panel -->
      <div
        class="absolute bottom-0 left-0 right-0 overflow-y-auto rounded-t-3xl"
        :style="{
          height: `${(props.verticalProgress || (isExpanded ? 1 : 0)) * 67}%`,
          backgroundColor: `${themeColor}E6`, // 90% opacity
          backdropFilter: 'blur(12px)',
          transform: `translateY(${(1 - (props.verticalProgress || (isExpanded ? 1 : 0))) * 100}%)`,
          transition: props.verticalProgress ? 'none' : 'all 0.5s ease-out',
          zIndex: 10
        }"
      >
        <div
          v-if="(props.verticalProgress && props.verticalProgress > 0) || isExpanded"
          class="p-8"
          :style="{
            opacity: Math.max(0, (props.verticalProgress || (isExpanded ? 1 : 0)) - 0.05) / 0.95,
            transition: props.verticalProgress ? 'none' : 'opacity 0.3s ease-out'
          }"
        >
          <!-- Close handle -->
          <div class="flex justify-center pt-2 pb-6">
            <button
              @click="$emit('collapse')"
              class="w-16 h-1.5 bg-white/30 rounded-full hover:bg-white/50 transition-colors"
            ></button>
          </div>

          <!-- Header with book info -->
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-white mb-2">{{ book.title }}</h2>
            <p class="text-white/90 mb-3 text-xl">by {{ book.author }}</p>
            <span class="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-bold text-white border border-white/30">
              {{ book.genre }}
            </span>
          </div>

          <!-- Rating -->
          <div class="mb-8 text-center">
            <div class="flex items-center justify-center gap-2 mb-2">
              <div class="flex items-center gap-1">
                <template v-for="(star, index) in communityStars" :key="index">
                  <div class="relative w-5 h-5">
                    <svg class="w-5 h-5 text-white/30 absolute" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <svg
                      v-if="star === 'full'"
                      class="w-5 h-5 text-bee absolute"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <svg
                      v-else-if="star === 'half'"
                      class="w-5 h-5 text-bee absolute overflow-hidden"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      style="clip-path: inset(0 50% 0 0)"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                  </div>
                </template>
              </div>
              <span class="text-white font-bold text-lg">{{ book.averageRating.toFixed(1) }}</span>
              <span class="text-white/80 text-sm">({{ book.ratingCount.toLocaleString() }} ratings)</span>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-4 mb-8">
            <button class="flex-1 bg-white/20 backdrop-blur-sm text-white py-4 px-8 rounded-2xl font-bold hover:bg-white/30 transition-all duration-300 hover:scale-105 shadow-lg border border-white/30">
              Start Reading
            </button>
            <button
              @click="handleBookmarkClick"
              class="px-6 py-4 rounded-2xl font-bold transition-all duration-300 hover:scale-105 shadow-lg"
              :class="isBookmarked ? 'bg-bee text-eel hover:bg-fox' : 'bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 border border-white/30'"
            >
              <span class="text-2xl">{{ isBookmarked ? '★' : '☆' }}</span>
            </button>
          </div>

          <!-- Synopsis -->
          <div class="mb-8">
            <h3 class="text-xl font-bold text-white mb-4 text-center">Synopsis</h3>
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <p class="text-white leading-relaxed text-base">
                {{ book.synopsis || book.firstParagraph }}
              </p>
            </div>
          </div>

          <!-- Your Rating -->
          <div class="text-center">
            <h3 class="text-xl font-bold text-white mb-4">Your Rating</h3>
            <div class="flex items-center justify-center gap-1">
              <template v-for="(filled, index) in userStars" :key="index">
                <button
                  @click="handleRatingClick(index + 1)"
                  class="transition-all duration-150 hover:scale-110"
                >
                  <svg
                    class="w-8 h-8"
                    :class="filled ? 'text-bee' : 'text-white/30 hover:text-bee/70'"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
