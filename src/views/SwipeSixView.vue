<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBooksStore } from '@/stores/books'
import SwipeSixCard from '@/components/swipe-six/SwipeSixCard.vue'
import SwipeSixDetails from '@/components/swipe-six/SwipeSixDetails.vue'

const booksStore = useBooksStore()

// Component state
const isAnimating = ref(false)
const showDetails = ref(false)
const currentIndex = ref(0)

// Touch/swipe handling
const startX = ref(0)
const startY = ref(0)
const currentX = ref(0)
const currentY = ref(0)
const isDragging = ref(false)
const swipeDirection = ref<'horizontal' | 'vertical' | null>(null)

// Animation states
const horizontalProgress = ref(0) // -1 to 1 for horizontal swipe progress
const verticalProgress = ref(0)   // 0 to 1 for vertical pull-up progress

const currentBook = computed(() => booksStore.currentBook)
const nextBook = computed(() => booksStore.getBookAtOffset(1))
const prevBook = computed(() => booksStore.getBookAtOffset(-1))

// Swipe thresholds
const HORIZONTAL_THRESHOLD = 100
const VERTICAL_THRESHOLD = 80

function handleTouchStart(event: TouchEvent): void {
  if (isAnimating.value) return
  
  startX.value = event.touches[0].clientX
  startY.value = event.touches[0].clientY
  isDragging.value = true
  swipeDirection.value = null
}

function handleTouchMove(event: TouchEvent): void {
  if (!isDragging.value || isAnimating.value) return
  
  currentX.value = event.touches[0].clientX
  currentY.value = event.touches[0].clientY
  
  const deltaX = currentX.value - startX.value
  const deltaY = currentY.value - startY.value
  
  // Determine swipe direction if not already set
  if (!swipeDirection.value && (Math.abs(deltaX) > 15 || Math.abs(deltaY) > 15)) {
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      swipeDirection.value = 'horizontal'
    } else {
      swipeDirection.value = 'vertical'
    }
  }
  
  // Update progress based on direction
  if (swipeDirection.value === 'horizontal') {
    const screenWidth = window.innerWidth
    horizontalProgress.value = Math.max(-1, Math.min(1, deltaX / screenWidth))
  } else if (swipeDirection.value === 'vertical' && !showDetails.value && deltaY < 0) {
    // Only allow upward swipe when not showing details
    const screenHeight = window.innerHeight
    verticalProgress.value = Math.max(0, Math.min(1, Math.abs(deltaY) / (screenHeight * 0.3)))
  }
}

function handleTouchEnd(): void {
  if (!isDragging.value) return
  
  const deltaX = currentX.value - startX.value
  const deltaY = currentY.value - startY.value
  
  if (swipeDirection.value === 'horizontal' && Math.abs(deltaX) > HORIZONTAL_THRESHOLD) {
    if (deltaX > 0) {
      handleSwipeRight()
    } else {
      handleSwipeLeft()
    }
  } else if (swipeDirection.value === 'vertical' && Math.abs(deltaY) > VERTICAL_THRESHOLD && deltaY < 0 && !showDetails.value) {
    handleSwipeUp()
  } else {
    // Snap back to current state
    resetProgress()
  }
  
  isDragging.value = false
  swipeDirection.value = null
}

function handleSwipeLeft(): void {
  if (isAnimating.value) return
  
  isAnimating.value = true
  
  // Animate to completion
  const animation = () => {
    horizontalProgress.value = Math.max(-1, horizontalProgress.value - 0.05)
    if (horizontalProgress.value > -1) {
      requestAnimationFrame(animation)
    } else {
      // Complete the swipe
      booksStore.swipeLeft()
      resetProgress()
      isAnimating.value = false
    }
  }
  requestAnimationFrame(animation)
}

function handleSwipeRight(): void {
  if (isAnimating.value) return
  
  isAnimating.value = true
  
  // Animate to completion
  const animation = () => {
    horizontalProgress.value = Math.min(1, horizontalProgress.value + 0.05)
    if (horizontalProgress.value < 1) {
      requestAnimationFrame(animation)
    } else {
      // Complete the swipe (for now, both directions advance)
      booksStore.swipeLeft()
      resetProgress()
      isAnimating.value = false
    }
  }
  requestAnimationFrame(animation)
}

function handleSwipeUp(): void {
  if (isAnimating.value || showDetails.value) return
  
  isAnimating.value = true
  showDetails.value = true
  
  // Animate to completion
  const animation = () => {
    verticalProgress.value = Math.min(1, verticalProgress.value + 0.03)
    if (verticalProgress.value < 1) {
      requestAnimationFrame(animation)
    } else {
      isAnimating.value = false
    }
  }
  requestAnimationFrame(animation)
}

function handleCloseDetails(): void {
  if (isAnimating.value) return
  
  isAnimating.value = true
  
  // Animate back to collapsed state
  const animation = () => {
    verticalProgress.value = Math.max(0, verticalProgress.value - 0.05)
    if (verticalProgress.value > 0) {
      requestAnimationFrame(animation)
    } else {
      showDetails.value = false
      isAnimating.value = false
    }
  }
  requestAnimationFrame(animation)
}

function resetProgress(): void {
  horizontalProgress.value = 0
  verticalProgress.value = 0
}

// Keyboard shortcuts for desktop
function handleKeydown(event: KeyboardEvent): void {
  if (event.key === 'ArrowLeft') {
    handleSwipeLeft()
  } else if (event.key === 'ArrowRight') {
    handleSwipeRight()
  } else if (event.key === 'ArrowUp') {
    handleSwipeUp()
  } else if (event.key === 'Escape' && showDetails.value) {
    handleCloseDetails()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="relative h-screen overflow-hidden">
    <!-- Main Card View -->
    <div
      v-if="!showDetails"
      class="h-full"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <SwipeSixCard
        v-if="currentBook"
        :book="currentBook"
        :next-book="nextBook"
        :prev-book="prevBook"
        :horizontal-progress="horizontalProgress"
        :vertical-progress="verticalProgress"
        :is-dragging="isDragging"
      />
      
      <!-- No more books message -->
      <div v-else class="flex items-center justify-center h-full bg-eel text-white text-center">
        <div>
          <p class="text-xl mb-4 font-bold">You've seen all the books!</p>
          <button
            @click="booksStore.resetSwipeSession"
            class="px-6 py-3 bg-feather text-white rounded-2xl hover:bg-mask font-bold transition-colors"
          >
            Start Over
          </button>
        </div>
      </div>
    </div>

    <!-- Details View -->
    <SwipeSixDetails
      v-if="showDetails && currentBook"
      :book="currentBook"
      :vertical-progress="verticalProgress"
      @close="handleCloseDetails"
    />

    <!-- Pagination Dots -->
    <div 
      v-if="!showDetails && currentBook"
      class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2"
    >
      <div
        v-for="i in Math.min(5, booksStore.books.length)"
        :key="i"
        class="w-2 h-2 rounded-full transition-all duration-300"
        :class="i === (currentIndex % 5) + 1 ? 'bg-white' : 'bg-white/30'"
      ></div>
    </div>

    <!-- Desktop Instructions -->
    <div class="hidden md:block absolute top-4 left-4 right-4 text-center text-white/70 text-sm">
      <p class="font-medium">Arrow keys: ← → to navigate • ↑ for details • ESC to close</p>
    </div>

    <!-- Mobile Instructions -->
    <div class="md:hidden absolute top-4 left-4 right-4 text-center text-white/70 text-sm">
      <p class="font-medium">Swipe horizontally to browse • Swipe up for details</p>
    </div>
  </div>
</template>
