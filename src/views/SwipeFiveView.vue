<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBooksStore } from '@/stores/books'
import SwipeFiveCard from '@/components/swipe-five/SwipeFiveCard.vue'

const booksStore = useBooksStore()

// Component state
const isAnimating = ref(false)
const isExpanded = ref(false)

// Touch/swipe handling
const startX = ref(0)
const startY = ref(0)
const currentX = ref(0)
const currentY = ref(0)
const isDragging = ref(false)
const cardTransform = ref('')
const swipeDirection = ref<'horizontal' | 'vertical' | null>(null)
const verticalProgress = ref(0) // 0 to 1 for vertical pull progress

const currentBook = computed(() => booksStore.currentBook)

// Swipe thresholds (in pixels)
const HORIZONTAL_THRESHOLD = 100
const VERTICAL_THRESHOLD = 150 // Increased to match new distance calculation

function handleTouchStart(event: TouchEvent): void {
  startX.value = event.touches[0].clientX
  startY.value = event.touches[0].clientY
  isDragging.value = true
  swipeDirection.value = null
}

function handleTouchMove(event: TouchEvent): void {
  if (!isDragging.value) return

  currentX.value = event.touches[0].clientX
  currentY.value = event.touches[0].clientY

  const deltaX = currentX.value - startX.value
  const deltaY = currentY.value - startY.value

  // Determine swipe direction if not already set
  if (!swipeDirection.value && (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10)) {
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      swipeDirection.value = 'horizontal'
    } else {
      swipeDirection.value = 'vertical'
    }
  }

  // Apply appropriate transform based on direction
  if (swipeDirection.value === 'horizontal') {
    const rotation = deltaX * 0.05
    cardTransform.value = `translateX(${deltaX}px) rotate(${rotation}deg)`
  } else if (swipeDirection.value === 'vertical') {
    const screenHeight = window.innerHeight
    const maxPullDistance = screenHeight * 0.67 // Match the panel height (67% of screen)

    if (!isExpanded.value && deltaY < 0) {
      // Upward swipe when collapsed - calculate progress based on actual panel height
      const progress = Math.min(Math.abs(deltaY) / maxPullDistance, 1)
      verticalProgress.value = progress
    } else if (isExpanded.value && deltaY > 0) {
      // Downward swipe when expanded - calculate reverse progress
      const progress = Math.max(1 - (deltaY / maxPullDistance), 0)
      verticalProgress.value = progress
      // Don't apply any transform to the card during downward pull
      cardTransform.value = ''
    }
  }
}

function handleTouchEnd(): void {
  if (!isDragging.value) return

  const deltaX = currentX.value - startX.value
  const deltaY = currentY.value - startY.value

  if (swipeDirection.value === 'horizontal' && Math.abs(deltaX) > HORIZONTAL_THRESHOLD) {
    if (deltaX > 0) {
      handleSwipeRight()
    } else {
      handleSwipeLeft()
    }
  } else if (swipeDirection.value === 'vertical' && Math.abs(deltaY) > VERTICAL_THRESHOLD) {
    if (deltaY < 0 && !isExpanded.value) {
      handleSwipeUp()
    } else if (deltaY > 0 && isExpanded.value) {
      handleSwipeDown()
    } else {
      // Snap back to current state
      cardTransform.value = ''
    }
  } else {
    // Snap back to center/current state
    cardTransform.value = ''
    // Reset vertical progress if gesture wasn't completed
    if (swipeDirection.value === 'vertical') {
      verticalProgress.value = isExpanded.value ? 1 : 0
    }
  }

  isDragging.value = false
  swipeDirection.value = null
}

function handleSwipeLeft(): void {
  if (isAnimating.value) return

  isAnimating.value = true
  cardTransform.value = 'translateX(-100vw) rotate(-15deg)'

  setTimeout(() => {
    booksStore.swipeLeft()
    cardTransform.value = ''
    isAnimating.value = false
    isExpanded.value = false // Reset to collapsed state for new book
  }, 400)
}

function handleSwipeRight(): void {
  if (isAnimating.value) return

  isAnimating.value = true
  cardTransform.value = 'translateX(100vw) rotate(15deg)'

  setTimeout(() => {
    booksStore.swipeLeft() // Both directions advance for now
    cardTransform.value = ''
    isAnimating.value = false
    isExpanded.value = false // Reset to collapsed state for new book
  }, 400)
}

function handleSwipeUp(): void {
  if (isAnimating.value || isExpanded.value) return

  isAnimating.value = true
  isExpanded.value = true

  // Animate to completion
  const animation = () => {
    verticalProgress.value = Math.min(1, verticalProgress.value + 0.05)
    if (verticalProgress.value < 1) {
      requestAnimationFrame(animation)
    } else {
      isAnimating.value = false
    }
  }
  requestAnimationFrame(animation)
}

function handleSwipeDown(): void {
  if (isAnimating.value || !isExpanded.value) return

  isAnimating.value = true

  // Animate back to collapsed state
  const animation = () => {
    verticalProgress.value = Math.max(0, verticalProgress.value - 0.05)
    if (verticalProgress.value > 0) {
      requestAnimationFrame(animation)
    } else {
      isExpanded.value = false
      isAnimating.value = false
    }
  }
  requestAnimationFrame(animation)
}

// Keyboard shortcuts for desktop testing
function handleKeydown(event: KeyboardEvent): void {
  if (event.key === 'ArrowLeft') {
    handleSwipeLeft()
  } else if (event.key === 'ArrowRight') {
    handleSwipeRight()
  } else if (event.key === 'ArrowUp') {
    handleSwipeUp()
  } else if (event.key === 'ArrowDown') {
    handleSwipeDown()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="relative h-[600px] bg-eel overflow-hidden">
    <!-- Main swipe area -->
    <div class="h-full">
      <div
        v-if="currentBook"
        class="h-full"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <SwipeFiveCard
          :book="currentBook"
          :transform="cardTransform"
          :is-expanded="isExpanded"
          :vertical-progress="verticalProgress"
          :class="{ 'transition-transform duration-500 ease-out': !isDragging }"
          @expand="handleSwipeUp"
          @collapse="handleSwipeDown"
        />
      </div>

      <!-- No more books message -->
      <div v-else class="flex items-center justify-center h-full text-center text-white">
        <div>
          <p class="text-xl mb-4 font-bold">You've seen all the books!</p>
          <button
            @click="booksStore.resetSwipeSession"
            class="px-6 py-3 bg-feather text-white rounded-2xl hover:bg-mask font-bold transition-colors"
          >
            Start Over
          </button>
        </div>
      </div>
    </div>

    <!-- Desktop instructions -->
    <div class="hidden md:block absolute bottom-4 left-4 right-4 text-center text-white/70 text-sm">
      <p class="font-medium">Arrow keys: ← → to navigate books • ↑ ↓ to expand/collapse details</p>
    </div>

    <!-- Mobile instructions -->
    <div class="md:hidden absolute bottom-4 left-4 right-4 text-center text-white/70 text-sm">
      <p class="font-medium">Swipe horizontally to browse • Swipe up/down for details</p>
    </div>
  </div>
</template>
