<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useBooksStore } from '@/stores/books'
import RatingStars from '@/components/RatingStars.vue'

const route = useRoute()
const id = computed(() => route.params.id as string)

const booksStore = useBooksStore()
const book = computed(() => booksStore.getBookById(id.value))

const rating = defineModel<number>({ default: 3 })
const isSubmitting = ref(false)

async function submitRating(): Promise<void> {
  if (!id.value) return
  isSubmitting.value = true
  try {
    await booksStore.rateBook(id.value, rating.value)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <div class="max-w-3xl mx-auto p-4">
    <RouterLink :to="{ name: 'browse' }" class="text-blue-600 hover:underline">← Back</RouterLink>
    <div v-if="book" class="mt-4 border rounded-lg p-4 bg-white shadow-sm">
      <h1 class="text-2xl font-bold">{{ book.title }}</h1>
      <p class="text-gray-500">by {{ book.author }}</p>
      <p class="text-sm text-blue-600 font-medium mt-1">{{ book.genre }}</p>
      <p class="mt-4 text-gray-800 whitespace-pre-line">{{ book.firstParagraph }}</p>

      <div class="mt-6 flex items-center gap-4">
        <RatingStars v-model="rating" />
        <button
          class="px-3 py-1.5 rounded-md bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50"
          :disabled="isSubmitting"
          @click="submitRating"
        >
          Submit rating
        </button>
        <div class="ml-auto text-sm text-gray-600">Community: ⭐ {{ book.averageRating.toFixed(1) }} ({{ book.ratingCount }})</div>
      </div>
    </div>
    <div v-else class="mt-4 text-gray-500">Book not found.</div>
  </div>
</template>
