<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBooksStore } from '@/stores/books'
import SwipeEightCard from '@/components/swipe-eight/SwipeEightCard.vue'

const booksStore = useBooksStore()

// Component state
const currentIndex = ref(0)
const isDragging = ref(false)
const dragOffset = ref(0)
const startX = ref(0)
const containerRef = ref<HTMLElement>()

// Get books as cards data
const cards = computed(() => 
  booksStore.books.map((book, index) => ({
    id: book.id,
    title: book.title,
    content: book.firstParagraph,
    color: getCardColor(book.genre),
    book: book
  }))
)

function getCardColor(genre: string): string {
  const colors = {
    'Fiction': 'bg-gradient-to-br from-macaw to-humpback',
    'Science Fiction': 'bg-gradient-to-br from-beetle to-humpback',
    'Fantasy': 'bg-gradient-to-br from-fox to-cardinal',
    'Romance': 'bg-gradient-to-br from-cardinal to-beetle',
    'Mystery': 'bg-gradient-to-br from-eel to-wolf',
    'Thriller': 'bg-gradient-to-br from-cardinal to-eel',
    'Horror': 'bg-gradient-to-br from-eel to-cardinal',
    'Adventure': 'bg-gradient-to-br from-feather to-macaw',
    'Historical Fiction': 'bg-gradient-to-br from-fox to-bee',
    'Literary Fiction': 'bg-gradient-to-br from-humpback to-beetle',
    'Young Adult': 'bg-gradient-to-br from-beetle to-macaw',
    'Non-Fiction': 'bg-gradient-to-br from-wolf to-hare',
    'Biography': 'bg-gradient-to-br from-bee to-fox',
    'Self-Help': 'bg-gradient-to-br from-feather to-mask',
    'Philosophy': 'bg-gradient-to-br from-humpback to-eel'
  }
  return colors[genre as keyof typeof colors] || 'bg-gradient-to-br from-macaw to-humpback'
}

function getCardAtIndex(index: number) {
  const normalizedIndex = ((index % cards.value.length) + cards.value.length) % cards.value.length
  return cards.value[normalizedIndex]
}

const visibleCards = computed(() => 
  Array.from({ length: 5 }, (_, i) => ({
    ...getCardAtIndex(currentIndex.value + i),
    displayIndex: currentIndex.value + i,
  }))
)

function handlePointerDown(e: PointerEvent): void {
  isDragging.value = true
  startX.value = e.clientX
  ;(e.currentTarget as HTMLElement).setPointerCapture(e.pointerId)
}

function handlePointerMove(e: PointerEvent): void {
  if (!isDragging.value) return

  const currentX = e.clientX
  const offset = currentX - startX.value
  dragOffset.value = offset
}

function handlePointerUp(e: PointerEvent): void {
  if (!isDragging.value) return

  isDragging.value = false
  ;(e.currentTarget as HTMLElement).releasePointerCapture(e.pointerId)

  const threshold = containerRef.value ? containerRef.value.offsetWidth * 0.25 : 100

  if (Math.abs(dragOffset.value) > threshold) {
    currentIndex.value = currentIndex.value + 1
  }

  dragOffset.value = 0
}

function getCardStyle(index: number) {
  const isActive = index === 0
  const dragProgress = Math.abs(dragOffset.value) / (containerRef.value?.offsetWidth || 400)

  if (isActive && isDragging.value) {
    const rotateY = (dragOffset.value / 200) * -15
    const rotateZ = (dragOffset.value / 300) * 8
    return {
      transform: `translateX(${dragOffset.value}px) rotateY(${rotateY}deg) rotateZ(${rotateZ}deg) translateZ(20px)`,
      zIndex: 20,
      opacity: 1 - dragProgress * 0.3,
    }
  }

  const baseRotations = [0, 6, 12, 18, 24]
  const baseScales = [1, 0.92, 0.84, 0.76, 0.68]
  const baseTranslateY = [0, -15, -30, -45, -60]
  const baseTranslateZ = [20, 8, -4, -16, -28]
  const baseOpacity = [1, 0.9, 0.8, 0.7, 0.6]

  const dragInfluence = isDragging.value && index > 0 ? dragProgress : 0
  const scale = baseScales[index] + dragInfluence * 0.08
  const translateY = baseTranslateY[index] + dragInfluence * 15
  const translateZ = baseTranslateZ[index] + dragInfluence * 6
  const rotateZ = baseRotations[index] - dragInfluence * 4
  const opacity = baseOpacity[index] + dragInfluence * 0.2

  return {
    transform: `scale(${scale}) translateY(${translateY}px) translateZ(${translateZ}px) rotateZ(${rotateZ}deg)`,
    zIndex: 10 - index,
    opacity: Math.min(opacity, 1),
  }
}

// Keyboard shortcuts for desktop testing
function handleKeydown(event: KeyboardEvent): void {
  if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
    currentIndex.value = currentIndex.value + 1
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="relative w-full h-screen bg-eel flex items-center justify-center px-4">
    <div 
      ref="containerRef" 
      class="relative w-full max-w-sm h-80 sm:w-80" 
      style="perspective: 1000px"
    >
      <div
        v-for="(card, index) in visibleCards"
        :key="`${card.id}-${card.displayIndex}`"
        class="absolute inset-0 cursor-grab active:cursor-grabbing transition-all duration-300 ease-out"
        :style="getCardStyle(index)"
        @pointerdown="index === 0 ? handlePointerDown($event) : undefined"
        @pointermove="index === 0 ? handlePointerMove($event) : undefined"
        @pointerup="index === 0 ? handlePointerUp($event) : undefined"
      >
        <SwipeEightCard
          :card="card"
          :index="index"
          :current-index="currentIndex"
          :cards-length="cards.length"
        />
      </div>
    </div>

    <!-- Pagination indicator -->
    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-2">
      <div class="text-white/60 text-sm">∞</div>
      <div class="flex space-x-1">
        <div
          v-for="index in Math.min(cards.length, 5)"
          :key="index"
          class="w-2 h-2 rounded-full transition-all duration-300"
          :class="index === 1 ? 'bg-white' : 'bg-white/30'"
        />
      </div>
    </div>

    <!-- Navigation hint -->
    <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white/50 text-sm text-center">
      <p>Drag cards to explore • Arrow keys for desktop</p>
    </div>
  </div>
</template>
