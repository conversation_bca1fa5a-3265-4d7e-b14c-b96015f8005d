<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBooksStore } from '@/stores/books'
import SwipeCard from '@/components/SwipeCard.vue'
import QuickRevealBanner from '@/components/QuickRevealBanner.vue'
import BookDetails from '@/components/BookDetails.vue'

const booksStore = useBooksStore()

// Component state
const showBanner = ref(false)
const showBookDetails = ref(false)
const lastSwipedBook = ref<string | null>(null)
const isAnimating = ref(false)
const bannerTimer = ref<number | null>(null)
const bannerHasTimer = ref(false) // Track if banner should show timer

// Touch/swipe handling
const startX = ref(0)
const currentX = ref(0)
const isDragging = ref(false)
const cardTransform = ref('')

const currentBook = computed(() => booksStore.currentBook)

// Swipe threshold (in pixels)
const SWIPE_THRESHOLD = 100

function handleTouchStart(event: TouchEvent): void {
  startX.value = event.touches[0].clientX
  isDragging.value = true
}

function handleTouchMove(event: TouchEvent): void {
  if (!isDragging.value) return

  currentX.value = event.touches[0].clientX
  const deltaX = currentX.value - startX.value

  // Apply transform to card
  const rotation = deltaX * 0.1 // Subtle rotation effect
  cardTransform.value = `translateX(${deltaX}px) rotate(${rotation}deg)`
}

function handleTouchEnd(): void {
  if (!isDragging.value) return

  const deltaX = currentX.value - startX.value

  if (Math.abs(deltaX) > SWIPE_THRESHOLD) {
    if (deltaX > 0) {
      handleSwipeRight()
    } else {
      handleSwipeLeft()
    }
  } else {
    // Snap back to center
    cardTransform.value = ''
  }

  isDragging.value = false
}

function handleSwipeLeft(): void {
  if (isAnimating.value) return

  isAnimating.value = true
  cardTransform.value = 'translateX(-100vw) rotate(-30deg)'

  // Clear any existing banner and timer when skipping
  if (bannerTimer.value) {
    clearTimeout(bannerTimer.value)
    bannerTimer.value = null
  }
  showBanner.value = false
  lastSwipedBook.value = null
  bannerHasTimer.value = false

  setTimeout(() => {
    booksStore.swipeLeft()
    cardTransform.value = ''
    isAnimating.value = false
  }, 300)
}

function handleSwipeRight(): void {
  if (isAnimating.value) return

  isAnimating.value = true
  lastSwipedBook.value = currentBook.value?.id || null
  cardTransform.value = 'translateX(100vw) rotate(30deg)'

  setTimeout(() => {
    booksStore.swipeRight()
    cardTransform.value = ''
    isAnimating.value = false

    // Show banner for the book that was just swiped right
    if (lastSwipedBook.value) {
      // Clear any existing timer first
      if (bannerTimer.value) {
        clearTimeout(bannerTimer.value)
        bannerTimer.value = null
      }

      // Show banner with timer
      showBanner.value = true
      bannerHasTimer.value = true

      // Set new timer for 7 seconds
      bannerTimer.value = setTimeout(() => {
        showBanner.value = false
        lastSwipedBook.value = null
        bannerTimer.value = null
        bannerHasTimer.value = false
      }, 7000)
    }
  }, 300)
}

function handleBannerTap(): void {
  // Clear the banner timer since user is interacting
  if (bannerTimer.value) {
    clearTimeout(bannerTimer.value)
    bannerTimer.value = null
  }
  bannerHasTimer.value = false
  showBanner.value = false
  showBookDetails.value = true
}

function handleCloseBookDetails(): void {
  showBookDetails.value = false
  // Show banner again without timer if we have a lastSwipedBook
  if (lastSwipedBook.value) {
    showBanner.value = true
    bannerHasTimer.value = false // No timer when returning from details
  }
}

function handleBackToSwipe(): void {
  showBookDetails.value = false
  // Show banner again without timer if we have a lastSwipedBook
  if (lastSwipedBook.value) {
    showBanner.value = true
    bannerHasTimer.value = false // No timer when returning from details
  }
}

function handleBannerClose(): void {
  // Clear the banner timer when manually closed
  if (bannerTimer.value) {
    clearTimeout(bannerTimer.value)
    bannerTimer.value = null
  }
  showBanner.value = false
  lastSwipedBook.value = null
  bannerHasTimer.value = false
}

// Keyboard shortcuts for desktop testing
function handleKeydown(event: KeyboardEvent): void {
  if (event.key === 'ArrowLeft') {
    handleSwipeLeft()
  } else if (event.key === 'ArrowRight') {
    handleSwipeRight()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // Clean up any pending timer
  if (bannerTimer.value) {
    clearTimeout(bannerTimer.value)
  }
})
</script>

<template>
  <div class="relative h-screen bg-gray-50 overflow-hidden">
    <!-- Main swipe area -->
    <div class="flex items-center justify-center h-full p-4">
      <div
        v-if="currentBook"
        class="relative w-full max-w-sm"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <SwipeCard
          :book="currentBook"
          :transform="cardTransform"
          :class="{ 'transition-transform duration-300 ease-out': !isDragging }"
        />
      </div>

      <!-- No more books message -->
      <div v-else class="text-center text-gray-500">
        <p class="text-xl mb-4">You've seen all the books!</p>
        <button
          @click="booksStore.resetSwipeSession"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Start Over
        </button>
      </div>
    </div>

    <!-- Desktop buttons -->
    <div class="hidden md:flex absolute bottom-20 left-1/2 transform -translate-x-1/2 gap-4">
      <button
        @click="handleSwipeLeft"
        class="px-6 py-3 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg flex items-center gap-2"
        :disabled="isAnimating"
      >
        <span>👈</span>
        <span>Skip</span>
      </button>
      <button
        @click="handleSwipeRight"
        class="px-6 py-3 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors shadow-lg flex items-center gap-2"
        :disabled="isAnimating"
      >
        <span>Intrigued</span>
        <span>👉</span>
      </button>
    </div>

    <!-- Swipe instructions -->
    <div class="absolute bottom-4 left-4 right-4 text-center text-gray-500 text-sm">
      <p class="md:hidden">Swipe left to skip • Swipe right if intrigued</p>
      <p class="hidden md:block">Use buttons above or arrow keys to navigate</p>
    </div>

    <!-- Quick Reveal Banner -->
    <QuickRevealBanner
      v-if="showBanner && lastSwipedBook"
      :key="`banner-${lastSwipedBook}-${bannerHasTimer}`"
      :book-id="lastSwipedBook"
      :has-timer="bannerHasTimer"
      @tap="handleBannerTap"
      @close="handleBannerClose"
    />

    <!-- Book Details Modal -->
    <BookDetails
      v-if="showBookDetails && lastSwipedBook"
      :book-id="lastSwipedBook"
      @close="handleCloseBookDetails"
      @back-to-swipe="handleBackToSwipe"
    />
  </div>
</template>
