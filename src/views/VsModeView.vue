<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useBooksStore } from '@/stores/books'
import BookVsDuel from '@/components/BookVsDuel.vue'

const booksStore = useBooksStore()

import type { Book } from '@/types/book'
const left = ref<Book | null>(null)
const right = ref<Book | null>(null)
const loading = ref(false)

async function loadDuel(): Promise<void> {
  loading.value = true
  try {
    const pair = await booksStore.getTwoRandomBooks()
    left.value = pair[0] || null
    right.value = pair[1] || null
  } finally {
    loading.value = false
  }
}

async function onChoose(id: string): Promise<void> {
  if (!left.value || !right.value) return
  await booksStore.submitVsVote(left.value.id, right.value.id, id)
  const loseId = id === left.value.id ? right.value.id : left.value.id
  await booksStore.incrementWinLoss(id, loseId)
  await loadDuel()
}

onMounted(loadDuel)
</script>

<template>
  <div class="max-w-5xl mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">VS Mode</h1>

    <div v-if="loading" class="text-gray-500">Loading…</div>
    <div v-else-if="left && right">
      <BookVsDuel :left="left" :right="right" @choose="onChoose" />
    </div>
    <div v-else class="text-gray-500">Not enough books yet. Add more books to play VS.</div>
  </div>
</template>
