<script setup lang="ts">
import { computed } from 'vue'
import { useBooksStore } from '@/stores/books'
import BookCard from '@/components/BookCard.vue'

const booksStore = useBooksStore()

const bookmarkedBooks = computed(() => {
  return booksStore.books.filter(book => booksStore.isBookmarked(book.id))
})
</script>

<template>
  <div class="max-w-5xl mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Your Bookmarks</h1>
    
    <div v-if="bookmarkedBooks.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <template v-for="book in bookmarkedBooks" :key="book.id">
        <BookCard :book="book" />
      </template>
    </div>
    
    <div v-else class="text-center py-12">
      <div class="text-6xl mb-4">📚</div>
      <h2 class="text-xl font-semibold text-gray-700 mb-2">No bookmarks yet</h2>
      <p class="text-gray-500 mb-6">
        Start swiping or browsing to bookmark books you're interested in!
      </p>
      <div class="flex gap-4 justify-center">
        <RouterLink to="/swipe" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
          Start Swiping
        </RouterLink>
        <RouterLink to="/browse" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          Browse Books
        </RouterLink>
      </div>
    </div>
  </div>
</template>
