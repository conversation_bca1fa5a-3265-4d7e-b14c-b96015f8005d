<script setup lang="ts">
import { useBooksStore } from '@/stores/books'
import BookCard from '@/components/BookCard.vue'

const booksStore = useBooksStore()
</script>

<template>
  <div class="max-w-5xl mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Browse first paragraphs</h1>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <template v-for="book in booksStore.books" :key="book.id">
        <BookCard :book="book" />
      </template>
    </div>
  </div>
</template>
