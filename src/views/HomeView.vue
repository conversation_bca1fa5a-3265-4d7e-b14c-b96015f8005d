<script setup lang="ts">
import { useBooksStore } from '@/stores/books'

const booksStore = useBooksStore()
</script>

<template>
  <div class="max-w-4xl mx-auto text-center">
    <h1 class="text-4xl font-bold text-eel mb-4">
      Book Openers
    </h1>
    <p class="text-xl text-wolf mb-8">
      Discover great books through their opening paragraphs
    </p>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-8 gap-6 mb-8">
      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe Mode</h2>
        <p class="text-wolf mb-4">
          Discover books by swiping through opening paragraphs. Swipe right for a quick reveal banner!
        </p>
        <RouterLink to="/swipe" class="inline-block px-4 py-2 bg-beetle text-white rounded-2xl hover:bg-humpback font-bold transition-colors">
          Start swiping
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe 2</h2>
        <p class="text-wolf mb-4">
          Swipe left if intrigued to see full book details immediately!
        </p>
        <RouterLink to="/swipe-two" class="inline-block px-4 py-2 bg-feather text-white rounded-2xl hover:bg-mask font-bold transition-colors">
          Try Swipe 2
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe 3</h2>
        <p class="text-wolf mb-4">
          Elegant card design with ratings and bookmarks. Like a physical book experience!
        </p>
        <RouterLink to="/swipe-three" class="inline-block px-4 py-2 bg-bee text-eel rounded-2xl hover:bg-fox font-bold transition-colors">
          Try Swipe 3
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe 4</h2>
        <p class="text-wolf mb-4">
          Same elegant layout as Swipe 3 but with vibrant Duolingo colors and bold styling!
        </p>
        <RouterLink to="/swipe-four" class="inline-block px-4 py-2 bg-feather text-white rounded-2xl hover:bg-mask font-bold transition-colors">
          Try Swipe 4
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe 5</h2>
        <p class="text-wolf mb-4">
          Full-screen cinematic experience! Swipe horizontally to browse, vertically to expand details.
        </p>
        <RouterLink to="/swipe-five" class="inline-block px-4 py-2 bg-macaw text-white rounded-2xl hover:bg-humpback font-bold transition-colors">
          Try Swipe 5
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe 6</h2>
        <p class="text-wolf mb-4">
          Advanced parallax animations with 3-layer transitions and shared element details!
        </p>
        <RouterLink to="/swipe-six" class="inline-block px-4 py-2 bg-beetle text-white rounded-2xl hover:bg-humpback font-bold transition-colors">
          Try Swipe 6
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe 7</h2>
        <p class="text-wolf mb-4">
          Four-part FLIP animation system with poster transformation and staggered content reveal!
        </p>
        <RouterLink to="/swipe-seven" class="inline-block px-4 py-2 bg-cardinal text-white rounded-2xl hover:bg-fox font-bold transition-colors">
          Try Swipe 7
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Swipe 8</h2>
        <p class="text-wolf mb-4">
          3D card stacking swiper with perspective transforms and smooth drag interactions!
        </p>
        <RouterLink to="/swipe-eight" class="inline-block px-4 py-2 bg-humpback text-white rounded-2xl hover:bg-beetle font-bold transition-colors">
          Try Swipe 8
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">Browse</h2>
        <p class="text-wolf mb-4">
          Explore {{ booksStore.books.length }} carefully curated first paragraphs from classic and contemporary literature.
        </p>
        <RouterLink to="/browse" class="inline-block px-4 py-2 bg-macaw text-white rounded-2xl hover:bg-humpback font-bold transition-colors">
          Start browsing
        </RouterLink>
      </div>

      <div class="bg-white rounded-2xl p-6 shadow-lg border-2 border-swan">
        <h2 class="text-2xl font-bold mb-3 text-eel">VS Mode</h2>
        <p class="text-wolf mb-4">
          Compare opening paragraphs head-to-head and vote for your favorites.
        </p>
        <RouterLink to="/vs" class="inline-block px-4 py-2 bg-fox text-white rounded-2xl hover:bg-cardinal font-bold transition-colors">
          Start comparing
        </RouterLink>
      </div>
    </div>

    <div class="text-center">
      <p class="text-gray-500 mb-4">Rate books, discover new favorites, and see how your taste compares to others.</p>
      <RouterLink to="/bookmarks" class="inline-flex items-center gap-2 px-4 py-2 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 transition-colors">
        <span>⭐</span>
        <span>View Your Bookmarks</span>
      </RouterLink>
    </div>
  </div>
</template>
