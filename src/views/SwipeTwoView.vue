<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBooksStore } from '@/stores/books'
import SwipeTwoCard from '@/components/swipe-two/SwipeTwoCard.vue'
import SwipeTwoBookDetails from '@/components/swipe-two/SwipeTwoBookDetails.vue'

const booksStore = useBooksStore()

// Component state
const showBookDetails = ref(false)
const currentBookForDetails = ref<string | null>(null)
const isAnimating = ref(false)
const showCurrentBookParagraph = ref(false) // Track if we should show current book's paragraph

// Touch/swipe handling
const startX = ref(0)
const currentX = ref(0)
const isDragging = ref(false)
const cardTransform = ref('')

const currentBook = computed(() => {
  // If we're showing current book paragraph, show the book from details
  if (showCurrentBookParagraph.value && currentBookForDetails.value) {
    return booksStore.getBookById(currentBookForDetails.value)
  }
  // Otherwise show the normal current book
  return booksStore.currentBook
})

// Swipe threshold (in pixels)
const SWIPE_THRESHOLD = 100

function handleTouchStart(event: TouchEvent): void {
  if (showBookDetails.value) return // Don't handle swipes when book details are shown
  startX.value = event.touches[0].clientX
  isDragging.value = true
}

function handleTouchMove(event: TouchEvent): void {
  if (!isDragging.value || showBookDetails.value) return

  currentX.value = event.touches[0].clientX
  const deltaX = currentX.value - startX.value

  // Apply transform to card
  const rotation = deltaX * 0.1 // Subtle rotation effect
  cardTransform.value = `translateX(${deltaX}px) rotate(${rotation}deg)`
}

function handleTouchEnd(): void {
  if (!isDragging.value || showBookDetails.value) return

  const deltaX = currentX.value - startX.value

  if (Math.abs(deltaX) > SWIPE_THRESHOLD) {
    if (deltaX > 0) {
      handleSwipeRight()
    } else {
      handleSwipeLeft()
    }
  } else {
    // Snap back to center
    cardTransform.value = ''
  }

  isDragging.value = false
}

function handleSwipeLeft(): void {
  if (isAnimating.value || showBookDetails.value) return

  isAnimating.value = true
  currentBookForDetails.value = currentBook.value?.id || null
  cardTransform.value = 'translateX(-100vw) rotate(-30deg)'

  setTimeout(() => {
    if (!showCurrentBookParagraph.value) {
      // Only advance to next book if not showing current book paragraph
      booksStore.swipeRight()
    }
    cardTransform.value = ''
    isAnimating.value = false
    showCurrentBookParagraph.value = false

    // Show book details immediately after swipe left (intrigued)
    if (currentBookForDetails.value) {
      showBookDetails.value = true
    }
  }, 400)
}

function handleSwipeRight(): void {
  if (isAnimating.value || showBookDetails.value) return

  isAnimating.value = true
  cardTransform.value = 'translateX(100vw) rotate(30deg)'

  setTimeout(() => {
    if (showCurrentBookParagraph.value) {
      // If showing current book paragraph, advance to next book
      showCurrentBookParagraph.value = false
      currentBookForDetails.value = null
    }
    booksStore.swipeLeft()
    cardTransform.value = ''
    isAnimating.value = false
  }, 400)
}

function handleBackToParagraph(): void {
  showBookDetails.value = false
  showCurrentBookParagraph.value = true
  // Keep currentBookForDetails to show the same book's paragraph
}

function handleNextParagraph(): void {
  showBookDetails.value = false
  showCurrentBookParagraph.value = false
  currentBookForDetails.value = null
  // This will advance to the next book in the normal flow
}

// Keyboard shortcuts for desktop testing
function handleKeydown(event: KeyboardEvent): void {
  if (showBookDetails.value) return // Don't handle keyboard when book details are shown

  if (event.key === 'ArrowLeft') {
    handleSwipeLeft() // Left arrow = Intrigued
  } else if (event.key === 'ArrowRight') {
    handleSwipeRight() // Right arrow = Skip
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="relative h-screen-safe bg-polar overflow-hidden">
    <!-- Main swipe area (hidden when book details are shown) -->
    <div v-if="!showBookDetails" class="flex items-center justify-center h-full p-4">
      <div
        v-if="currentBook"
        class="relative w-full max-w-sm"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <SwipeTwoCard
          :book="currentBook"
          :transform="cardTransform"
          :class="{ 'transition-transform duration-500 ease-out': !isDragging }"
        />
      </div>

      <!-- No more books message -->
      <div v-else class="text-center text-wolf">
        <p class="text-xl mb-4 font-bold text-eel">You've seen all the books!</p>
        <button
          @click="booksStore.resetSwipeSession"
          class="px-6 py-3 bg-feather text-white rounded-2xl hover:bg-mask font-bold transition-colors"
        >
          Start Over
        </button>
      </div>
    </div>

    <!-- Desktop buttons (hidden when book details are shown) -->
    <div v-if="!showBookDetails" class="hidden md:flex absolute bottom-20 left-1/2 transform -translate-x-1/2 gap-4">
      <button
        @click="handleSwipeLeft"
        class="px-6 py-3 bg-feather text-white rounded-2xl hover:bg-mask transition-colors shadow-lg flex items-center gap-2 font-bold"
        :disabled="isAnimating"
      >
        <span>👈</span>
        <span>Intrigued</span>
      </button>
      <button
        @click="handleSwipeRight"
        class="px-6 py-3 bg-cardinal text-white rounded-2xl hover:bg-fox transition-colors shadow-lg flex items-center gap-2 font-bold"
        :disabled="isAnimating"
      >
        <span>Skip</span>
        <span>👉</span>
      </button>
    </div>

    <!-- Swipe instructions (hidden when book details are shown) -->
    <div v-if="!showBookDetails" class="absolute bottom-4 left-4 right-4 text-center text-wolf text-sm">
      <p class="md:hidden font-medium">Swipe left if intrigued • Swipe right to skip</p>
      <p class="hidden md:block font-medium">Use buttons above or arrow keys to navigate</p>
    </div>

    <!-- Book Details (replaces the card view) -->
    <SwipeTwoBookDetails
      v-if="showBookDetails && currentBookForDetails"
      :book-id="currentBookForDetails"
      @back-to-paragraph="handleBackToParagraph"
      @next-paragraph="handleNextParagraph"
    />
  </div>
</template>
