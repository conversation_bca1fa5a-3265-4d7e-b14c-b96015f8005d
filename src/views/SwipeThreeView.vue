<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBooksStore } from '@/stores/books'
import SwipeThreeCard from '@/components/swipe-three/SwipeThreeCard.vue'

const booksStore = useBooksStore()

// Component state
const isAnimating = ref(false)

// Touch/swipe handling
const startX = ref(0)
const currentX = ref(0)
const isDragging = ref(false)
const cardTransform = ref('')

const currentBook = computed(() => booksStore.currentBook)

// Swipe threshold (in pixels)
const SWIPE_THRESHOLD = 100

function handleTouchStart(event: TouchEvent): void {
  startX.value = event.touches[0].clientX
  isDragging.value = true
}

function handleTouchMove(event: TouchEvent): void {
  if (!isDragging.value) return

  currentX.value = event.touches[0].clientX
  const deltaX = currentX.value - startX.value

  // Apply transform to card
  const rotation = deltaX * 0.05 // Subtle rotation effect
  cardTransform.value = `translateX(${deltaX}px) rotate(${rotation}deg)`
}

function handleTouchEnd(): void {
  if (!isDragging.value) return

  const deltaX = currentX.value - startX.value

  if (Math.abs(deltaX) > SWIPE_THRESHOLD) {
    if (deltaX > 0) {
      handleSwipeRight()
    } else {
      handleSwipeLeft()
    }
  } else {
    // Snap back to center
    cardTransform.value = ''
  }

  isDragging.value = false
}

function handleSwipeLeft(): void {
  if (isAnimating.value) return

  isAnimating.value = true
  cardTransform.value = 'translateX(-100vw) rotate(-15deg)'

  setTimeout(() => {
    booksStore.swipeLeft()
    cardTransform.value = ''
    isAnimating.value = false
  }, 400)
}

function handleSwipeRight(): void {
  if (isAnimating.value) return

  isAnimating.value = true
  cardTransform.value = 'translateX(100vw) rotate(15deg)'

  setTimeout(() => {
    booksStore.swipeLeft() // Both directions advance for now
    cardTransform.value = ''
    isAnimating.value = false
  }, 400)
}

// Keyboard shortcuts for desktop testing
function handleKeydown(event: KeyboardEvent): void {
  if (event.key === 'ArrowLeft') {
    handleSwipeLeft()
  } else if (event.key === 'ArrowRight') {
    handleSwipeRight()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="relative h-screen-safe bg-book-bg overflow-hidden">
    <!-- Main swipe area -->
    <div class="flex items-center justify-center h-full p-4">
      <div
        v-if="currentBook"
        class="relative w-full max-w-2xl"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <SwipeThreeCard
          :book="currentBook"
          :transform="cardTransform"
          :class="{ 'transition-transform duration-500 ease-out': !isDragging }"
        />
      </div>

      <!-- No more books message -->
      <div v-else class="text-center text-wolf">
        <p class="text-xl mb-4 font-bold text-eel">You've seen all the books!</p>
        <button
          @click="booksStore.resetSwipeSession"
          class="px-6 py-3 bg-feather text-white rounded-2xl hover:bg-mask font-bold transition-colors"
        >
          Start Over
        </button>
      </div>
    </div>

    <!-- Desktop buttons -->
    <div class="hidden md:flex absolute bottom-20 left-1/2 transform -translate-x-1/2 gap-4">
      <button
        @click="handleSwipeLeft"
        class="px-6 py-3 bg-cardinal text-white rounded-2xl hover:bg-fox transition-colors shadow-lg flex items-center gap-2 font-bold"
        :disabled="isAnimating"
      >
        <span>👈</span>
        <span>Pass</span>
      </button>
      <button
        @click="handleSwipeRight"
        class="px-6 py-3 bg-feather text-white rounded-2xl hover:bg-mask transition-colors shadow-lg flex items-center gap-2 font-bold"
        :disabled="isAnimating"
      >
        <span>Like</span>
        <span>👉</span>
      </button>
    </div>

    <!-- Swipe instructions -->
    <div class="absolute bottom-4 left-4 right-4 text-center text-wolf text-sm">
      <p class="md:hidden font-medium">Swipe left to pass • Swipe right to like</p>
      <p class="hidden md:block font-medium">Use buttons above or arrow keys to navigate</p>
    </div>
  </div>
</template>
