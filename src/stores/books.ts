import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { books as booksData } from '@/data/books'
import type { Book, BookRating, BookVsVote } from '@/types/book'

export const useBooksStore = defineStore('books', () => {
  // State
  const ratings = ref<BookRating[]>([])
  const vsVotes = ref<BookVsVote[]>([])
  const bookmarks = ref<Set<string>>(new Set())
  const bookStats = ref<Map<string, { totalRating: number; ratingCount: number; wins: number; losses: number }>>(new Map())

  // Swipe state
  const currentBookIndex = ref(0)
  const swipeHistory = ref<string[]>([]) // Track swiped books to avoid repeats

  // Initialize book stats
  booksData.forEach(book => {
    bookStats.value.set(book.id, {
      totalRating: book.averageRating * book.ratingCount,
      ratingCount: book.ratingCount,
      wins: 0,
      losses: 0
    })
  })

  // Getters
  const booksWithUpdatedRatings = computed(() => {
    return booksData.map(book => {
      const stats = bookStats.value.get(book.id)
      if (!stats) return book

      return {
        ...book,
        averageRating: stats.ratingCount > 0 ? stats.totalRating / stats.ratingCount : book.averageRating,
        ratingCount: stats.ratingCount
      }
    })
  })

  const currentBook = computed(() => {
    const availableBooks = booksWithUpdatedRatings.value.filter(book =>
      !swipeHistory.value.includes(book.id)
    )

    if (availableBooks.length === 0) {
      // Reset history if all books have been swiped
      swipeHistory.value = []
      return booksWithUpdatedRatings.value[0]
    }

    const index = currentBookIndex.value % availableBooks.length
    return availableBooks[index]
  })

  const isBookmarked = computed(() => (bookId: string) => bookmarks.value.has(bookId))

  // Actions
  function getBookById(id: string): Book | undefined {
    const book = booksData.find(b => b.id === id)
    if (!book) return undefined

    const stats = bookStats.value.get(id)
    if (!stats) return book

    return {
      ...book,
      averageRating: stats.ratingCount > 0 ? stats.totalRating / stats.ratingCount : book.averageRating,
      ratingCount: stats.ratingCount
    }
  }

  function toggleBookmark(bookId: string): void {
    if (bookmarks.value.has(bookId)) {
      bookmarks.value.delete(bookId)
    } else {
      bookmarks.value.add(bookId)
    }
  }

  function swipeLeft(): void {
    if (currentBook.value) {
      swipeHistory.value.push(currentBook.value.id)
      currentBookIndex.value++
    }
  }

  function swipeRight(): void {
    if (currentBook.value) {
      swipeHistory.value.push(currentBook.value.id)
      currentBookIndex.value++
    }
  }

  async function rateBook(bookId: string, rating: number, userId?: string): Promise<void> {
    const newRating: BookRating = {
      id: `rating_${Date.now()}_${Math.random()}`,
      bookId,
      rating,
      userId: userId ?? undefined,
      createdAt: new Date()
    }
    ratings.value.push(newRating)

    const stats = bookStats.value.get(bookId)
    if (stats) {
      stats.totalRating += rating
      stats.ratingCount += 1
      bookStats.value.set(bookId, stats)
    }
  }

  async function getTwoRandomBooks(): Promise<Book[]> {
    if (booksWithUpdatedRatings.value.length < 2) return booksWithUpdatedRatings.value

    const shuffled = [...booksWithUpdatedRatings.value].sort(() => Math.random() - 0.5)
    return shuffled.slice(0, 2)
  }

  async function submitVsVote(leftId: string, rightId: string, chosenId: string, userId?: string): Promise<void> {
    const vote: BookVsVote = {
      id: `vote_${Date.now()}_${Math.random()}`,
      leftId,
      rightId,
      chosenId,
      userId: userId ?? undefined,
      createdAt: new Date()
    }
    vsVotes.value.push(vote)
  }

  async function incrementWinLoss(winId: string, loseId: string): Promise<void> {
    const winStats = bookStats.value.get(winId)
    const loseStats = bookStats.value.get(loseId)

    if (winStats) {
      winStats.wins += 1
      bookStats.value.set(winId, winStats)
    }

    if (loseStats) {
      loseStats.losses += 1
      bookStats.value.set(loseId, loseStats)
    }
  }

  function resetSwipeSession(): void {
    swipeHistory.value = []
    currentBookIndex.value = 0
  }

  function getBookAtOffset(offset: number): Book | null {
    const availableBooks = booksWithUpdatedRatings.value.filter(book =>
      !swipeHistory.value.includes(book.id)
    )

    if (availableBooks.length === 0) return null

    const currentIndex = currentBookIndex.value % availableBooks.length
    const targetIndex = currentIndex + offset

    if (targetIndex < 0 || targetIndex >= availableBooks.length) return null

    return availableBooks[targetIndex]
  }

  return {
    // State
    bookmarks,
    currentBook,
    swipeHistory,

    // Getters
    books: booksWithUpdatedRatings,
    isBookmarked,

    // Actions
    getBookById,
    getBookAtOffset,
    toggleBookmark,
    swipeLeft,
    swipeRight,
    rateBook,
    getTwoRandomBooks,
    submitVsVote,
    incrementWinLoss,
    resetSwipeSession
  }
})
