import { initializeApp } from 'firebase/app'
import { getFirestore, connectFirestoreEmulator, initializeFirestore } from 'firebase/firestore'

// Keep config in .env as VITE_FIREBASE_*
// This file only reads the values and initializes the Firebase app for VueFire to use.
export const firebaseApp = initializeApp({
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY as string,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN as string,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID as string,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET as string,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID as string,
  appId: import.meta.env.VITE_FIREBASE_APP_ID as string,
})

// Initialize Firestore once to be able to configure it (e.g., emulator)
// We ignore undefined properties to avoid writing undefined to Firestore
initializeFirestore(firebaseApp, { ignoreUndefinedProperties: true })

const db = getFirestore(firebaseApp)

// In development, prefer the local emulator to avoid hitting remote APIs
// Set VITE_USE_FIRESTORE_EMULATOR=false to opt-out
if (import.meta.env.DEV && import.meta.env.VITE_USE_FIRESTORE_EMULATOR !== 'false') {
  const hostPort = (import.meta.env.VITE_FIRESTORE_EMULATOR_HOST as string | undefined) || '127.0.0.1:8080'
  const [host, portStr] = hostPort.split(':')
  const port = Number(portStr || 8080)
  try {
    connectFirestoreEmulator(db, host, port)
    // Comment: We avoid logging to keep console clean; emulator connection will route all Firestore traffic locally.
  } catch {
    // noop: connecting twice throws in HMR; safe to ignore in dev
  }
}

export type { FirebaseApp } from 'firebase/app'
export { db }
