import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/browse',
      name: 'browse',
      component: () => import('../views/BrowseView.vue'),
    },
    {
      path: '/book/:id',
      name: 'book',
      component: () => import('../views/BookView.vue'),
      props: true,
    },
    {
      path: '/vs',
      name: 'vs',
      component: () => import('../views/VsModeView.vue'),
    },
    {
      path: '/swipe',
      name: 'swipe',
      component: () => import('../views/SwipeView.vue'),
    },
    {
      path: '/swipe-two',
      name: 'swipe-two',
      component: () => import('../views/SwipeTwoView.vue'),
    },
    {
      path: '/swipe-three',
      name: 'swipe-three',
      component: () => import('../views/SwipeThreeView.vue'),
    },
    {
      path: '/swipe-four',
      name: 'swipe-four',
      component: () => import('../views/SwipeFourView.vue'),
    },
    {
      path: '/swipe-five',
      name: 'swipe-five',
      component: () => import('../views/SwipeFiveView.vue'),
    },
    {
      path: '/swipe-six',
      name: 'swipe-six',
      component: () => import('../views/SwipeSixView.vue'),
    },
    {
      path: '/swipe-seven',
      name: 'swipe-seven',
      component: () => import('../views/SwipeSevenView.vue')
    },
    {
      path: '/swipe-eight',
      name: 'swipe-eight',
      component: () => import('../views/SwipeEightView.vue')
    },
    {
      path: '/bookmarks',
      name: 'bookmarks',
      component: () => import('../views/BookmarksView.vue'),
    },
  ],
})

export default router
