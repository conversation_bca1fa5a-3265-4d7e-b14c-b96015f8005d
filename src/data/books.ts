import type { Book } from '@/types/book';

export const books: Book[] = [
  {
    id: 'dune',
    title: 'Du<PERSON>',
    author: '<PERSON>',
    genre: 'Science Fiction',
    firstParagraph:
      'In the week before their departure to Arrakis, when all the final scurrying about had reached a nearly unbearable frenzy, an old crone came to visit the mother of the boy, <PERSON>.',
    synopsis: 'Set on the desert planet Arrakis, <PERSON><PERSON> is the story of the boy <PERSON>, heir to a noble family tasked with ruling an inhospitable world where the only thing of value is the "spice" melange, a drug capable of extending life and enhancing consciousness.',
    coverImageId: 'img_dune',
    averageRating: 4.8,
    ratingCount: 1250,
    amazonUrl: 'https://amazon.com/dp/0441172717',
  },
  {
    id: '1984',
    title: 'Nineteen Eighty-Four',
    author: '<PERSON>',
    genre: 'Dystopian',
    firstParagraph:
      'It was a bright cold day in April, and the clocks were striking thirteen.',
    synopsis: 'A dystopian social science fiction novel that follows <PERSON>, a low-ranking member of the ruling Party in London, in the nation of Oceania, where the Party exercises total control over the populace.',
    coverImageId: 'img_1984',
    averageRating: 4.7,
    ratingCount: 2340,
    amazonUrl: 'https://amazon.com/dp/0452284236',
  },
  {
    id: 'pride-and-prejudice',
    title: 'Pride and Prejudice',
    author: 'Jane <PERSON>',
    genre: 'Romance',
    firstParagraph:
      'It is a truth universally acknowledged, that a single man in possession of a good fortune, must be in want of a wife.',
    synopsis: 'A romantic novel that follows the character development of <PERSON> Bennet, the dynamic protagonist who learns about the repercussions of hasty judgments and comes to appreciate the difference between superficial goodness and actual goodness.',
    coverImageId: 'img_pride',
    averageRating: 4.9,
    ratingCount: 3100,
    amazonUrl: 'https://amazon.com/dp/0141439513',
  },
  {
    id: 'the-hobbit',
    title: 'The Hobbit',
    author: 'J.R.R. Tolkien',
    genre: 'Fantasy',
    firstParagraph:
      'In a hole in the ground there lived a hobbit. Not a nasty, dirty, wet hole, filled with the ends of worms and an oozy smell, nor yet a dry, bare, sandy hole with nothing in it to sit down on or to eat: it was a hobbit-hole, and that means comfort.',
    coverImageId: 'img_hobbit',
    averageRating: 4.9,
    ratingCount: 4500,
  },
  {
    id: 'great-gatsby',
    title: 'The Great Gatsby',
    author: 'F. Scott Fitzgerald',
    genre: 'Classic',
    firstParagraph:
      'In my younger and more vulnerable years my father gave me some advice that I\'ve been turning over in my mind ever since.',
    coverImageId: 'img_gatsby',
    averageRating: 4.5,
    ratingCount: 1800,
  },
  {
    id: 'moby-dick',
    title: 'Moby Dick',
    author: 'Herman Melville',
    genre: 'Adventure',
    firstParagraph: 'Call me Ishmael.',
    coverImageId: 'img_moby',
    averageRating: 4.2,
    ratingCount: 980,
  },
  {
    id: 'catcher-in-the-rye',
    title: 'The Catcher in the Rye',
    author: 'J.D. Salinger',
    genre: 'Classic',
    firstParagraph:
      'If you really want to hear about it, the first thing you\'ll probably want to know is where I was born, and what my lousy childhood was like, and how my parents were occupied and all before they had me, and all that David Copperfield kind of crap, but I don\'t feel like going into it, if you want to know the truth.',
    coverImageId: 'img_catcher',
    averageRating: 4.3,
    ratingCount: 1500,
  },
  {
    id: 'to-kill-a-mockingbird',
    title: 'To Kill a Mockingbird',
    author: 'Harper Lee',
    genre: 'Classic',
    firstParagraph:
      'When he was nearly thirteen, my brother Jem got his arm badly broken at the elbow.',
    coverImageId: 'img_mockingbird',
    averageRating: 4.8,
    ratingCount: 3200,
  },
  {
    id: 'fahrenheit-451',
    title: 'Fahrenheit 451',
    author: 'Ray Bradbury',
    genre: 'Dystopian',
    firstParagraph: 'It was a pleasure to burn.',
    coverImageId: 'img_fahrenheit',
    averageRating: 4.6,
    ratingCount: 2100,
  },
  {
    id: 'the-alchemist',
    title: 'The Alchemist',
    author: 'Paulo Coelho',
    genre: 'Fantasy',
    firstParagraph:
      'The boy\'s name was Santiago. Dusk was falling as the boy arrived with his herd at an abandoned church.',
    coverImageId: 'img_alchemist',
    averageRating: 4.7,
    ratingCount: 2800,
  },
  {
    id: 'enders-game',
    title: "Ender's Game",
    author: 'Orson Scott Card',
    genre: 'Science Fiction',
    firstParagraph:
      '"I\'ve watched through his eyes, I\'ve listened through his ears, and I tell you he\'s the one."',
    coverImageId: 'img_enders',
    averageRating: 4.5,
    ratingCount: 1950,
  },
  {
    id: 'hitchhikers-guide',
    title: "The Hitchhiker's Guide to the Galaxy",
    author: 'Douglas Adams',
    genre: 'Science Fiction',
    firstParagraph:
      'Far out in the uncharted backwaters of the unfashionable end of the western spiral arm of the Galaxy lies a small unregarded yellow sun.',
    coverImageId: 'img_hitchhiker',
    averageRating: 4.8,
    ratingCount: 3300,
  },
   {
    id: 'jane-eyre',
    title: 'Jane Eyre',
    author: 'Charlotte Brontë',
    genre: 'Romance',
    firstParagraph: 'There was no possibility of taking a walk that day.',
    coverImageId: 'img_jane_eyre',
    averageRating: 4.6,
    ratingCount: 1700,
  },
  {
    id: 'frankenstein',
    title: 'Frankenstein',
    author: 'Mary Shelley',
    genre: 'Gothic',
    firstParagraph: 'You will rejoice to hear that no disaster has accompanied the commencement of an enterprise which you have regarded with such evil forebodings.',
    coverImageId: 'img_frankenstein',
    averageRating: 4.4,
    ratingCount: 1650,
  },
  {
    id: 'slaughterhouse-five',
    title: 'Slaughterhouse-Five',
    author: 'Kurt Vonnegut',
    genre: 'Satire',
    firstParagraph: 'All this happened, more or less.',
    coverImageId: 'img_slaughterhouse',
    averageRating: 4.5,
    ratingCount: 1400,
  },
];
