<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
</script>

<template>
  <div class="min-h-dvh bg-polar text-eel">
    <header class="border-b-2 border-swan bg-white shadow-sm h-16">
      <nav class="max-w-6xl mx-auto p-4 flex items-center gap-4 overflow-x-auto">
        <RouterLink to="/swipe" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe</RouterLink>
        <RouterLink to="/swipe-two" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe 2</RouterLink>
        <RouterLink to="/swipe-three" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe 3</RouterLink>
        <RouterLink to="/swipe-four" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe 4</RouterLink>
        <RouterLink to="/swipe-five" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe 5</RouterLink>
        <RouterLink to="/swipe-six" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe 6</RouterLink>
        <RouterLink to="/swipe-seven" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe 7</RouterLink>
        <RouterLink to="/swipe-eight" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Swipe 8</RouterLink>
        <RouterLink to="/browse" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Browse</RouterLink>
        <RouterLink to="/vs" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">VS Mode</RouterLink>
        <RouterLink to="/bookmarks" class="text-eel hover:text-feather font-medium transition-colors whitespace-nowrap">Bookmarks</RouterLink>
      </nav>
    </header>
    <main class="max-w-6xl mx-auto p-4">
      <RouterView />
    </main>
  </div>
</template>
