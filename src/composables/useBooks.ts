import { ref, computed } from 'vue'
import { books } from '@/data/books'
import type { Book, BookRating, BookVsVote } from '@/types/book'

// In-memory storage for ratings and votes (simulating a database)
const ratings = ref<BookRating[]>([])
const vsVotes = ref<BookVsVote[]>([])
const bookStats = ref<Map<string, { totalRating: number; ratingCount: number; wins: number; losses: number }>>(new Map())

// Initialize book stats
books.forEach(book => {
  bookStats.value.set(book.id, {
    totalRating: book.averageRating * book.ratingCount,
    ratingCount: book.ratingCount,
    wins: 0,
    losses: 0
  })
})

export function useBooks() {
  const booksWithUpdatedRatings = computed(() => {
    return books.map(book => {
      const stats = bookStats.value.get(book.id)
      if (!stats) return book
      
      return {
        ...book,
        averageRating: stats.ratingCount > 0 ? stats.totalRating / stats.ratingCount : 0,
        ratingCount: stats.ratingCount
      }
    })
  })

  const hasAny = computed(() => books.length > 0)

  function getBookById(id: string): Book | undefined {
    const book = books.find(b => b.id === id)
    if (!book) return undefined
    
    const stats = bookStats.value.get(id)
    if (!stats) return book
    
    return {
      ...book,
      averageRating: stats.ratingCount > 0 ? stats.totalRating / stats.ratingCount : book.averageRating,
      ratingCount: stats.ratingCount
    }
  }

  async function rateBook(bookId: string, rating: number, userId?: string): Promise<void> {
    // Add rating to in-memory storage
    const newRating: BookRating = {
      id: `rating_${Date.now()}_${Math.random()}`,
      bookId,
      rating,
      userId: userId ?? undefined,
      createdAt: new Date()
    }
    ratings.value.push(newRating)

    // Update book stats
    const stats = bookStats.value.get(bookId)
    if (stats) {
      stats.totalRating += rating
      stats.ratingCount += 1
      bookStats.value.set(bookId, stats)
    }
  }

  async function getTwoRandomBooks(): Promise<Book[]> {
    if (books.length < 2) return books
    
    const shuffled = [...booksWithUpdatedRatings.value].sort(() => Math.random() - 0.5)
    return shuffled.slice(0, 2)
  }

  async function submitVsVote(leftId: string, rightId: string, chosenId: string, userId?: string): Promise<void> {
    const vote: BookVsVote = {
      id: `vote_${Date.now()}_${Math.random()}`,
      leftId,
      rightId,
      chosenId,
      userId: userId ?? undefined,
      createdAt: new Date()
    }
    vsVotes.value.push(vote)
  }

  async function incrementWinLoss(winId: string, loseId: string): Promise<void> {
    const winStats = bookStats.value.get(winId)
    const loseStats = bookStats.value.get(loseId)
    
    if (winStats) {
      winStats.wins += 1
      bookStats.value.set(winId, winStats)
    }
    
    if (loseStats) {
      loseStats.losses += 1
      bookStats.value.set(loseId, loseStats)
    }
  }

  return {
    books: booksWithUpdatedRatings,
    hasAny,
    getBookById,
    rateBook,
    getTwoRandomBooks,
    submitVsVote,
    incrementWinLoss
  }
}
