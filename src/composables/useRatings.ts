import { useFirestore } from 'vuefire'
import { addDoc, collection, doc, runTransaction, serverTimestamp } from 'firebase/firestore'
import type { Rating } from '@/types/paragraph'

export function useRatings() {
  const db = useFirestore()
  const ratingsCol = collection(db, 'ratings')

  async function rateParagraph(paragraphId: string, rating: number, userId?: string): Promise<void> {
    const paraRef = doc(db, 'paragraphs', paragraphId)

    await runTransaction(db, async (tx) => {
      const paraSnap = await tx.get(paraRef)
      if (!paraSnap.exists()) throw new Error('Paragraph not found')
      const data = paraSnap.data() as { avgRating?: number; ratingsCount?: number }
      const currentAvg = data.avgRating ?? 0
      const count = data.ratingsCount ?? 0
      const newCount = count + 1
      const newAvg = (currentAvg * count + rating) / newCount

      tx.update(paraRef, {
        avgRating: newAvg,
        ratingsCount: newCount,
      })

      await addDoc(ratingsCol, {
        paragraphId,
        rating,
        userId: userId ?? null,
        createdAt: serverTimestamp(),
      } as Rating)
    })
  }

  return { rateParagraph }
}
