import { computed } from 'vue'
import { useFirestore, useCollection, useDocument } from 'vuefire'
import { addDoc, collection, doc, getDocs, limit, orderBy, query, serverTimestamp, updateDoc, increment } from 'firebase/firestore'
import type { Paragraph } from '@/types/paragraph'

export function useParagraphs() {
  const db = useFirestore()
  const paragraphsCol = collection(db, 'paragraphs')
  const q = query(paragraphsCol, orderBy('createdAt', 'desc'), limit(50))
  const paragraphs = useCollection<Paragraph>(q, { ssrKey: 'paragraphs-latest-50' })

  const hasAny = computed(() => (paragraphs.value?.length ?? 0) > 0)

  async function addSampleParagraphs(): Promise<void> {
    const snap = await getDocs(paragraphsCol)
    if (!snap.empty) return

    const samples: Paragraph[] = [
      {
        bookTitle: 'Moby-<PERSON>',
        author: '<PERSON>',
        text: 'Call me <PERSON><PERSON><PERSON>. Some years ago—never mind how long precisely...',
        createdAt: new Date(),
        avgRating: 0,
        ratingsCount: 0,
        wins: 0,
        losses: 0,
      },
      {
        bookTitle: 'Pride and Prejudice',
        author: '<PERSON>',
        text: 'It is a truth universally acknowledged, that a single man in possession of a good fortune...',
        createdAt: new Date(),
        avgRating: 0,
        ratingsCount: 0,
        wins: 0,
        losses: 0,
      },
      {
        bookTitle: '1984',
        author: 'George Orwell',
        text: 'It was a bright cold day in April, and the clocks were striking thirteen.',
        createdAt: new Date(),
        avgRating: 0,
        ratingsCount: 0,
        wins: 0,
        losses: 0,
      },
    ]

    for (const p of samples) {
      await addDoc(paragraphsCol, {
        ...p,
        createdAt: serverTimestamp() as unknown as Date,
      })
    }
  }

  function paragraphDoc(id: string) {
    return doc(db, 'paragraphs', id)
  }

  function useParagraph(id: string) {
    const d = paragraphDoc(id)
    return useDocument<Paragraph>(d)
  }

  async function incrementWinLoss(winId: string, loseId: string): Promise<void> {
    await updateDoc(paragraphDoc(winId), {
      wins: increment(1),
    })
    await updateDoc(paragraphDoc(loseId), {
      losses: increment(1),
    })
  }

  return { paragraphs, hasAny, addSampleParagraphs, useParagraph, paragraphDoc, incrementWinLoss }
}
