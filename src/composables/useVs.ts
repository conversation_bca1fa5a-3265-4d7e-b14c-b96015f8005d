import { useFirestore } from 'vuefire'
import { addDoc, collection, getDocs, limit, query, serverTimestamp } from 'firebase/firestore'
import type { VsVote, Paragraph } from '@/types/paragraph'

export function useVs() {
  const db = useFirestore()
  const paragraphsCol = collection(db, 'paragraphs')
  const vsVotesCol = collection(db, 'vsVotes')

  async function getTwoRandomParagraphs(): Promise<Paragraph[]> {
    // For simplicity, we fetch up to 20 paragraphs and pick two at random on client side.
    const q = query(paragraphsCol, limit(20))
    const snap = await getDocs(q)
    const all: (Paragraph & { id: string })[] = snap.docs.map((d) => ({ id: d.id, ...(d.data() as Paragraph) }))
    if (all.length < 2) return all
    const i = Math.floor(Math.random() * all.length)
    let j = Math.floor(Math.random() * all.length)
    if (j === i) j = (j + 1) % all.length
    return [all[i], all[j]]
  }

  async function submitVsVote(leftId: string, rightId: string, chosenId: string, userId?: string): Promise<void> {
    await addDoc(vsVotesCol, {
      leftId,
      rightId,
      chosenId,
      userId: userId ?? null,
      createdAt: serverTimestamp(),
    } as VsVote)
  }

  return { getTwoRandomParagraphs, submitVsVote }
}
