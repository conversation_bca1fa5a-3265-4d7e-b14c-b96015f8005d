@import "tailwindcss";

@theme {
  /* Duolingo-inspired animal color palette */
  --color-feather: #58CC02;    /* Primary green */
  --color-mask: #89E219;       /* Light green */
  --color-macaw: #1CB0F6;      /* Blue */
  --color-cardinal: #FF4B4B;   /* Red */
  --color-bee: #FFC800;        /* Yellow */
  --color-fox: #FF9600;        /* Orange */
  --color-beetle: #CE82FF;     /* Purple */
  --color-humpback: #2B70C9;   /* Dark blue */
  --color-eel: #4B4B4B;        /* Dark gray */
  --color-wolf: #777777;       /* Medium gray */
  --color-hare: #AFAFAF;       /* Light gray */
  --color-swan: #E5E5E5;       /* Border gray */
  --color-polar: #F7F7F7;      /* Background gray */

  /* Elegant card design colors (for swipe-three) */
  --color-book-bg: hsl(38 33% 96%);        /* Light, warm gray background */
  --color-book-text: hsl(210 25% 20%);     /* Dark, desaturated blue text */
  --color-book-primary: hsl(210 25% 28%);  /* Slightly darker blue for headings */
  --color-book-muted: hsl(210 20% 45%);    /* Soft gray-blue for subtle text */
  --color-book-accent: hsl(37 90% 51%);    /* Vibrant yellow-orange for accents */

  /* Custom height utilities */
  --height-screen-safe: calc(100vh - 4rem);
  --min-height-screen-safe: calc(100vh - 4rem);
}
