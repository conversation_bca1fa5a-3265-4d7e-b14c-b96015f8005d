export interface Paragraph {
  id?: string
  bookTitle: string
  author: string
  text: string
  createdAt: Date
  avgRating?: number
  ratingsCount?: number
  wins?: number
  losses?: number
}

export interface Rating {
  id?: string
  paragraphId: string
  userId?: string
  rating: number
  createdAt: Date
}

export interface VsVote {
  id?: string
  leftId: string
  rightId: string
  chosenId: string
  userId?: string
  createdAt: Date
}
