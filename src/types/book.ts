export interface Book {
  id: string;
  title: string;
  author: string;
  genre: string;
  firstParagraph: string;
  synopsis?: string;
  coverImageId: string;
  averageRating: number;
  ratingCount: number;
  amazonUrl?: string;
}

export interface BookRating {
  id?: string;
  bookId: string;
  userId?: string;
  rating: number;
  createdAt: Date;
}

export interface BookVsVote {
  id?: string;
  leftId: string;
  rightId: string;
  chosenId: string;
  userId?: string;
  createdAt: Date;
}
