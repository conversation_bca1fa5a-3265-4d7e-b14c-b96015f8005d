<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#58CC02">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Book Openers">
    <meta name="msapplication-TileColor" content="#58CC02">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- App Info -->
    <title>Book Openers - Discover Your Next Read</title>
    <meta name="description" content="Discover your next favorite book by swiping through captivating opening paragraphs">
    <meta name="keywords" content="books, reading, literature, book discovery, opening paragraphs">
    <meta name="author" content="Book Openers">

    <!-- Open Graph -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Book Openers - Discover Your Next Read">
    <meta property="og:description" content="Discover your next favorite book by swiping through captivating opening paragraphs">
    <meta property="og:url" content="https://book-openers.app">
    <meta property="og:site_name" content="Book Openers">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Book Openers - Discover Your Next Read">
    <meta name="twitter:description" content="Discover your next favorite book by swiping through captivating opening paragraphs">

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/book-icon.svg">
    <link rel="icon" type="image/svg+xml" href="/icons/book-icon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/book-icon.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/book-icon.svg">
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
